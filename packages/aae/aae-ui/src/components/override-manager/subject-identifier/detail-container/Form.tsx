import { useContext, useEffect, useState } from "react";
import {
  defaultFormProps,
  TextWithTooltip,
  Field,
  Input,
  mergeFormValues,
  InlineDatePicker,
} from "@zscaler/zui-component-library";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/pro-solid-svg-icons";
import { noop } from "lodash-es";
import { CRUDPageContext } from "../../../../contexts/CRUDPageContextProvider";
import { type FormValues, getFormValidationDetail } from "./helper";
import { FormValidationDetail } from "../../../../types/modalhelper";
import dayjs from "dayjs";

const Form: React.FC = () => {
  const [validationDetail, setValidationDetail] =
    useState<FormValidationDetail>({});
  const { detail, setDetail, setModalMode } = useContext(CRUDPageContext)!;

  const [formValues, setFormValues] = useState<FormValues>({
    ...detail,
  });

  useEffect(() => {
    setDetail({ ...formValues });

    const formValidationDetail = getFormValidationDetail({ formValues });

    setValidationDetail(formValidationDetail);
  }, [formValues, setDetail, setValidationDetail]);

  const onFormFieldChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const formFiledData = mergeFormValues(evt) as FormValues;
    setFormValues(formFiledData);
  };

  const handleBackClick = () => {
    setModalMode("");
    setDetail({});
  };

  const renderGeneralSection = () => (
    <>
      <div
        className="flex items-center cursor-pointer"
        onClick={handleBackClick}
        onKeyDown={noop}
        tabIndex={0}
        role="button"
      >
        <FontAwesomeIcon
          style={{ color: "#2160E1" }}
          icon={faArrowLeft}
          className="icon left"
        />
        <TextWithTooltip
          containerStyle={{ fontSize: "1rem" }}
          text={detail.subjectName}
        />
      </div>
      <TextWithTooltip
        containerStyle={{
          marginTop: "8px",
          marginBottom: "8px",
          fontSize: "0.8rem",
        }}
        text={detail.contextLabel}
      />

      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <Field label="CONTEXT_VALUE">
          <TextWithTooltip
            containerStyle={{ marginTop: "8px" }}
            text={dayjs(Number(detail?.contextValue)).format(
              "MMMM DD, YYYY - hh:mm A",
            )}
          />
        </Field>

        <Input
          label="OVERRIDE_VALUE"
          name="overrideValue"
          onChange={onFormFieldChange}
          value={formValues.overrideValue ?? ""}
          maxLength={128}
          info={validationDetail}
        />
      </div>

      <InlineDatePicker
        name="overrideExpiry"
        label="OVERRIDE_EXPIRY"
        selectedDate={
          formValues?.overrideExpiry
            ? new Date(formValues.overrideExpiry * 1000)
            : null
        }
        containerStyle={{ width: "fit-content" }}
        onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
          const selectedDate = new Date(evt.target.value).getTime() / 1000;
          setFormValues((prevState) => ({
            ...prevState,
            overrideExpiry: Math.floor(selectedDate),
            overrideTimestamp: Math.floor(selectedDate),
          }));
        }}
        format="MMM DD YYYY, hh:mm A"
        info={validationDetail}
        showTimePicker
        showHourMinuteOption={false}
        showHourOption
        showMinuteOption
      />
    </>
  );

  return <section className="form-container">{renderGeneralSection()}</section>;
};

export default Form;
