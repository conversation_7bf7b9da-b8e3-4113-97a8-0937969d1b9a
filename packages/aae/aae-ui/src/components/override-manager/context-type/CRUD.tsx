import { useContext } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody } from "@zscaler/zui-component-library";

import NoAccess from "../../no-access/NoAccess";

import { CRUDPageContext } from "../../../contexts/CRUDPageContextProvider";
import Form from "./Form";

const CRUD = () => {
  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext)!;

  const { noAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(false);
    setDetail(defaultDetail);
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    modalMode && (
      <Modal
        align="right"
        isBlocking={false}
        show={modalMode}
        onEscape={onCloseClick}
        containerClass="crud-modal override-manager"
      >
        <ModalHeader text={detail.contextType} onClose={onCloseClick} />
        <ModalBody>
          {" "}
          <Form />
        </ModalBody>
      </Modal>
    )
  );
};

export default CRUD;
