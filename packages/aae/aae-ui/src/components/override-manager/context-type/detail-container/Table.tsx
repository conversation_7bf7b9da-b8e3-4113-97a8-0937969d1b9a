import type React from "react";
import { useCallback, useContext, useMemo } from "react";
import { useSelector } from "react-redux";

import { faEye, faPencilAlt } from "@fortawesome/pro-solid-svg-icons";
import { Actions, TableContainer } from "@zscaler/zui-component-library";

import { noop } from "lodash-es";

import { CRUDPageContext } from "../../../../contexts/CRUDPageContextProvider";
import { useContextTypeSelectors } from "../../../../ducks/context-type/selectors";
import { useContextType } from "../../../../ducks/context-type";
import { useApiCall } from "../../../../hooks/useApiCallContext";

type ActionsCellProps = {
  row: {
    original: {
      isSystemRole?: boolean;
    };
  };
};

const Table: React.FC = () => {
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, searchTerm, privileges } =
    useContext(CRUDPageContext)!;

  const { getList } = useContextType();
  const { tableConfig, tableDetail } = useContextTypeSelectors();

  const { hasFullAccess } = privileges;

  const onEditClick = useCallback(
    (detail: { isSystemRole?: boolean }) => {
      if (detail) {
        setDetail(detail);
        setModalMode(hasFullAccess && !detail.isSystemRole ? "edit" : "view");
      }
    },
    [hasFullAccess, setDetail, setModalMode],
  );

  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail) => {
        if (columnDetail.id === "actions") {
          const ActionsCellComponent: React.FC<ActionsCellProps> = ({
            row,
          }) => {
            const isSystemRole = row.original.isSystemRole;

            return (
              <Actions
                {...row}
                showEdit={true}
                editIcon={hasFullAccess && !isSystemRole ? faPencilAlt : faEye}
                onEditClick={() => onEditClick(row.original)}
                showDelete={false}
              />
            );
          };

          columnDetail.cell = ActionsCellComponent;
        }

        return columnDetail;
      }) || [],
    [tableConfig?.columns, hasFullAccess, onEditClick],
  );

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload: {
      requireTotal: boolean;
      pageOffset?: number;
      pageSize?: number;
      name?: string;
    } = {
      requireTotal: false,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    if (pageOffset && pageSize) {
      payload.pageOffset = pageOffset + pageSize;
    }

    apiCall(() => getList()).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default Table;
