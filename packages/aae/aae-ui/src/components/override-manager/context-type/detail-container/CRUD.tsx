import type React from "react";
import { useContext } from "react";
import {
  CRUDModal,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
} from "@zscaler/zui-component-library";

import NoAccess from "../../../no-access/NoAccess";
import { CRUDPageContext } from "../../../../contexts/CRUDPageContextProvider";
import { type FormProps } from "../../../../types/modalhelper";
import Form from "./Form";
import { modalModeDetail } from "./helper";
import { useGlobalContext } from "../../../../ducks/global";
import { useContextType } from "../../../../ducks/context-type";
import { TypePayload } from "../../../../ducks/subject-identifier";
import { useApiCall } from "../../../../hooks/useApiCallContext";

const CRUD: React.FC = () => {
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultModalMode,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext)!;
  const { showErrorNotification } = useGlobalContext();

  const { add, deleteData, update } = useContextType();

  const { hasFullAccess, noAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
  };

  const onSaveClick = () => {
    const updatedDetail: TypePayload = { ...detail };
    if (modalMode === "add") {
      apiCall(() => add(updatedDetail), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch((err: Error) => {
          showErrorNotification({
            message: err.message || "Error in adding data",
          });
        });
    }

    if (modalMode === "edit") {
      apiCall(() => update(updatedDetail), {
        successNotificationPayload: getApiPUTNotificationOptions(),
      })
        .then(onCloseClick)
        .catch((err: Error) => {
          showErrorNotification({
            message: err.message || "Error in update",
          });
        });
    }

    if (modalMode === "delete") {
      apiCall(() => deleteData(updatedDetail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch((err: Error) => {
          showErrorNotification({
            message: err.message || "Error in deleting data",
          });
        });
    }
  };

  if (noAccess) {
    return <NoAccess />;
  }

  const showSave = hasFullAccess && !detail.isSystemRole;
  const cancelText = hasFullAccess && !detail.isSystemRole ? "CANCEL" : "CLOSE";

  return (
    <CRUDModal
      mode={modalMode}
      containerClass={`${modalMode === "delete" ? "delete-modal-container" : ""}`}
      renderFormSection={(props: FormProps) => <Form {...props} />}
      saveText={modalMode === "actionConfirmation" ? "RESET" : ""}
      cancelText={cancelText}
      showSave={showSave}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...(modalModeDetail[`${modalMode}`] || {})}
    />
  );
};

export default CRUD;
