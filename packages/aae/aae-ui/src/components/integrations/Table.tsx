import type React from "react";
import { use<PERSON><PERSON>back, useMemo, useState } from "react";

import { faEye, faPencilAlt } from "@fortawesome/pro-solid-svg-icons";
import {
  Actions,
  TableContainer,
  TextWithTooltip,
} from "@zscaler/zui-component-library";

import { noop } from "lodash-es";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCircleCheck,
  faCircleXmark,
} from "@fortawesome/free-solid-svg-icons";

import OktaModal from "./okta/OktaModal";
import CrowdStrikeModal from "./crowd-strike/CrowdStrikeModal";
import { Privileges } from "../../types/permissions";
import { useIntegrations } from "../../ducks/integrations";
import { useIntegrationsSelectors } from "../../ducks/integrations/selectors";
import { useApiCall } from "../../hooks/useApiCallContext";
import MicrosoftDefenderModal from "./microsoft-defender/MicrosoftDefenderModal";
import SilverFortModal from "./silver-fort/SilverFortModal";

type TableProps = {
  privileges: Privileges;
};

type RowData = {
  isSystemRole?: boolean;
  streamStatus?: boolean;
  name?: string;
  contextType?: string;
};

const Table: React.FC<TableProps> = ({ privileges }) => {
  // Context Hooks
  const { apiCall } = useApiCall();
  const { getList, resetConfigs } = useIntegrations();
  const { tableConfig, tableDetail } = useIntegrationsSelectors();

  const [activeModal, setActiveModal] = useState("");
  const [modalMode, setModalMode] = useState("");

  const { hasFullAccess } = privileges;

  // Edit Handler
  const onEditClick = useCallback(
    (detail: RowData) => {
      if (detail) {
        setActiveModal(detail.name ?? "");
        setModalMode(hasFullAccess && !detail.isSystemRole ? "edit" : "view");
      }
    },
    [hasFullAccess],
  );

  // Column Customizations
  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail) => {
        if (columnDetail.id === "actions") {
          const ActionsCellComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const isSystemRole = props?.row?.original?.isSystemRole;

            return (
              <Actions
                {...props}
                showEdit={true}
                editIcon={hasFullAccess && !isSystemRole ? faPencilAlt : faEye}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
                showDelete={false}
              />
            );
          };

          return { ...columnDetail, cell: ActionsCellComponent };
        }

        if (columnDetail.id === "streamStatus") {
          const TextWithTooltipComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const { streamStatus } = props?.row?.original || {};

            return (
              <>
                <FontAwesomeIcon
                  color={streamStatus ? "green" : "#76829A"}
                  icon={streamStatus ? faCircleCheck : faCircleXmark}
                  className="icon left"
                />
                <TextWithTooltip text={streamStatus ? "Enabled" : "Disabled"} />
              </>
            );
          };

          return { ...columnDetail, cell: TextWithTooltipComponent };
        }

        if (columnDetail.id === "contextType") {
          const TextWithTooltipComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const { contextType } = props?.row?.original || {};

            return <TextWithTooltip text={contextType ?? ""} />;
          };

          return { ...columnDetail, cell: TextWithTooltipComponent };
        }

        return columnDetail;
      }) || [],
    [tableConfig?.columns, hasFullAccess, onEditClick],
  );

  // Delete Handler
  const onDeleteClick = useCallback((detail: RowData) => {
    if (detail) {
      setActiveModal(detail.name ?? "");
      setModalMode("delete");
    }
  }, []);

  // Load More Handler
  const onLoadMoreClick = useCallback(() => {
    // const { pageSize, pageOffset } = tableDetail;

    // const payload: GetListParams = {
    //   requireTotal: false,
    //   pageOffset: pageOffset + pageSize,
    //   pageSize,
    // };

    // if (searchTerm) {
    //   payload.name = searchTerm;
    // }

    apiCall(() => getList()).catch(noop);
  }, [tableDetail, getList]);

  // Close Modal Handler
  const onCloseClick = useCallback(() => {
    setModalMode("");
    setActiveModal("");
    resetConfigs();
  }, [resetConfigs]);

  return (
    <>
      <TableContainer
        {...tableConfig}
        columns={tableColumnConfig}
        data={tableDetail.data}
        pagination={{ ...tableDetail, onLoadMoreClick }}
      />
      {modalMode === "edit" && (
        <>
          {activeModal === "Okta" && (
            <OktaModal
              privileges={privileges}
              show={activeModal === "Okta"}
              onCloseClick={onCloseClick}
            />
          )}
          {activeModal === "CrowdStrike" && (
            <CrowdStrikeModal
              privileges={privileges}
              show={activeModal === "CrowdStrike"}
              onCloseClick={onCloseClick}
            />
          )}
          {activeModal === "Microsoft Defender" && (
            <MicrosoftDefenderModal
              privileges={privileges}
              show={activeModal === "Microsoft Defender"}
              onCloseClick={onCloseClick}
            />
          )}
          {activeModal === "Silverfort" && (
            <SilverFortModal
              privileges={privileges}
              show={activeModal === "Silverfort"}
              onCloseClick={onCloseClick}
            />
          )}
        </>
      )}
    </>
  );
};

export default Table;
