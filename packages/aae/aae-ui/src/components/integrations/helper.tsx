import { defaultValidationDetail } from "@zscaler/zui-component-library";

import { PERMISSIONS_KEY, PERMISSION_LEVEL } from "../../config";
import {
  type ActionOption,
  type FormValidationDetail,
  type ModalModeDetail,
  type PermissionKeyDetailType,
  type PermissionLevelDetailType,
  type TooltipDetail,
} from "../../types/modalhelper";

export const modalModeDetail: ModalModeDetail = {
  "": {},
  view: {
    headerText: "INTEGRATIONS",
  },
  add: {
    headerText: "ADD_INTEGRATIONS",
  },
  edit: {
    headerText: "EDIT_INTEGRATIONS",
  },
  delete: {
    headerText: "DELETE_INTEGRATIONS",
    confirmationMessage:
      "Are you sure you want to delete this Integration? The changes cannot be undone.",
  },
  bulkDelete: {
    headerText: "BULK_DELETE",
    confirmationMessage: "BULK_DELETE_ROLE_CONFIRMATION_MESSAGE",
  },
  importFile: {
    headerText: "IMPORT_ROLE",
  },
};

export const bulkActionOptions: ActionOption[] = [
  { label: "DELETE", value: "DELETE" },
];

export const defaultBulkActionOption: ActionOption = {
  label: "ACTIONS",
  value: "ACTIONS",
};

export const PERMISSIONS_LEVEL_DETAIL: PermissionLevelDetailType = {
  [PERMISSION_LEVEL.FULL]: {
    id: PERMISSION_LEVEL.FULL,
    label: "Full",
    order: 1,
  },
  [PERMISSION_LEVEL.RESTRICTED_FULL]: {
    id: PERMISSION_LEVEL.RESTRICTED_FULL,
    label: "Restricted Full",
    order: 2,
  },
  [PERMISSION_LEVEL.VIEW]: {
    id: PERMISSION_LEVEL.VIEW,
    label: "View Only",
    order: 3,
  },
  [PERMISSION_LEVEL.RESTRICTED_VIEW]: {
    id: PERMISSION_LEVEL.RESTRICTED_VIEW,
    label: "Restricted View",
    order: 4,
  },
  [PERMISSION_LEVEL.NONE]: {
    id: PERMISSION_LEVEL.NONE,
    label: "None",
    order: 5,
  },
};

export type FormValues = {
  baseUrl?: string;
  clientId?: string;
  customerId?: string;
  clientSecret?: string;
  sharedSecret?: string;
  jwksUrl?: string;
  streamStatus?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  contextSignals?: any[];
};

export const getFormValidationDetail = ({
  formValues,
  isCrowdStrike = false,
}: {
  formValues: FormValues;
  isCrowdStrike?: boolean;
}): FormValidationDetail => {
  const validationDetail = { ...defaultValidationDetail };

  const { baseUrl, clientId, customerId, clientSecret, sharedSecret } =
    formValues || {};

  if (!baseUrl) {
    validationDetail.isValid = false;
    validationDetail.context = "baseUrl";
    validationDetail.type = "error";
    validationDetail.message = "BASE_URL_REQUIRED_MESSAGE";

    return validationDetail;
  }

  if (!clientId) {
    validationDetail.isValid = false;
    validationDetail.context = "clientId";
    validationDetail.type = "error";
    validationDetail.message = "CLIENT_ID_REQUIRED_MESSAGE";

    return validationDetail;
  }

  if (isCrowdStrike) {
    if (!customerId) {
      validationDetail.isValid = false;
      validationDetail.context = "customerId";
      validationDetail.type = "error";
      validationDetail.message = "CUSTOMER_ID_REQUIRED_MESSAGE";

      return validationDetail;
    }

    if (!clientSecret) {
      validationDetail.isValid = false;
      validationDetail.context = "clientSecret";
      validationDetail.type = "error";
      validationDetail.message = "CLIENT_SECRET_REQUIRED_MESSAGE";

      return validationDetail;
    }

    if (!sharedSecret) {
      validationDetail.isValid = false;
      validationDetail.context = "sharedSecret";
      validationDetail.type = "error";
      validationDetail.message = "SHARED_SECRET_REQUIRED_MESSAGE";

      return validationDetail;
    }
  }

  return validationDetail;
};

export const PERMISSION_KEY_DETAILS: PermissionKeyDetailType = {
  [PERMISSIONS_KEY.SIGN_ON_POLICY]: {
    label: "SIGN_ON_POLICIES",
    tooltip: (
      <p>
        Set the access level to{" "}
        <strong className="tooltip-bold"> Policy & gt; Admin Sign - On </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  // Repeat the same pattern for other permission keys...
};

export const getFormTooltipDetail = (name: string): TooltipDetail => {
  const tooltipDetail: TooltipDetail = {
    content: "",
  };

  if (name === "name") {
    tooltipDetail.content = `Enter a name for the location`;
  } else if (name === "country") {
    tooltipDetail.content = `Select the country of the IP location`;
  } else if (name === "ipAddress") {
    tooltipDetail.content = (
      <p>
        Enter the IP address of the location and click{" "}
        <strong className="tooltip-bold"> Add Items </strong>. You can add
        multiple IP addresses for a location.Click{" "}
        <strong className="tooltip-bold"> X </strong> to remove an IP address or{" "}
        <strong className="tooltip-bold"> Remove All </strong> to remove all
        selections.
      </p>
    );
  } else if (name === "overrideExistingEntries") {
    tooltipDetail.content = (
      <p>
        Enable this option if you want to update your existing locations, delete
        existing locations, or add new locations.If you only want to add new
        locations, Zscaler doesn & apos;t recommend selecting this option.To
        learn more, see{" "}
        <a href="https://help.zscaler.com/zslogin/importing-ip-locations-csv-file">
          Importing IP Locations from a CSV File
        </a>
        .
      </p>
    );
  } else if (name === "csvFile") {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold"> Browse File </strong> and select
        the CSV file you want to import.
      </p>
    );
  }

  return tooltipDetail;
};
