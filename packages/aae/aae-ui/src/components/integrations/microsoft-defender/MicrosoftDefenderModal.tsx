import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Input,
  mergeFormValues,
  Field,
  Button,
  TableContainer,
  ToggleButton,
  PasswordInput,
} from "@zscaler/zui-component-library";
import { isEqual, noop } from "lodash-es";
import { useTranslation } from "react-i18next";
import { type FormValidationDetail } from "../../../types/modalhelper";
import { type Privileges } from "../../../types/permissions";
import { useGlobalContext } from "../../../ducks/global";
import { useIntegrations } from "../../../ducks/integrations";
import { useIntegrationsSelectors } from "../../../ducks/integrations/selectors";
import { useApiCall } from "../../../hooks/useApiCallContext";
import { getFormValidationDetail } from "./helper";

type MicrosoftDefenderModalProps = {
  show: boolean;
  onCloseClick: () => void;
  privileges: Privileges;
};

type FormValues = {
  connectionString?: string;
  streamStatus?: boolean;
  eventHubInstance?: string;
  eventHubHost?: string;
  consumerGroup?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  contextSignals?: any[];
};

export default function MicrosoftDefenderModal({
  show,
  onCloseClick,
  privileges,
}: MicrosoftDefenderModalProps) {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();
  const { hasFullAccess } = privileges;

  const {
    getMicrosoftDefenderConfigs,
    saveMicrosoftDefenderConfigs,
    validateMicrosoftDefenderConfigs,
  } = useIntegrations();
  const { configsDetail: oktaConfigs, commonTableConfig: tableConfig } =
    useIntegrationsSelectors();

  const { showErrorNotification, showSuccessNotification } = useGlobalContext();

  const [formValues, setFormValues] = useState<FormValues>({});
  const [validationDetail, setValidationDetail] =
    useState<FormValidationDetail>({});
  const [isSaveClicked, setIsSaveClicked] = useState(false);

  useEffect(() => {
    // Fetch CrowdStrike configurations and update relevant form values
    apiCall(getMicrosoftDefenderConfigs).catch(noop);
  }, [getMicrosoftDefenderConfigs]);

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...oktaConfigs }));
  }, [oktaConfigs]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onFormFieldChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const formFiledData = mergeFormValues(evt) as FormValues;
    setFormValues(formFiledData);
  };

  const onStreamStatusButtonsClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      streamStatus: !prevState.streamStatus,
    }));
  };

  const renderBodySection = () => (
    <>
      <Input
        name="eventHubInstance"
        label="EVENT_HUB_INSTANCE"
        onChange={onFormFieldChange}
        value={formValues.eventHubInstance ?? ""}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
        containerStyle={{ maxWidth: "35%" }}
      />

      <Input
        name="eventHubHost"
        label="EVENT_HUB_HOST"
        onChange={onFormFieldChange}
        value={formValues.eventHubHost ?? ""}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
        containerStyle={{ maxWidth: "35%" }}
      />

      <Input
        name="consumerGroup"
        label="CONSUMER_GROUP"
        onChange={onFormFieldChange}
        value={formValues.consumerGroup ?? ""}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
        containerStyle={{ maxWidth: "35%" }}
      />

      <PasswordInput
        name="connectionString"
        label="CONEECTION_STRING"
        onChange={onFormFieldChange}
        value={formValues?.connectionString ?? ""}
        maxLength={256}
        canCopy
        disabled={!hasFullAccess}
        info={validationDetail}
        containerStyle={{ maxWidth: "35%" }}
      />

      <Field label="STATUS">
        <ToggleButton
          type="success"
          showLabel={false}
          isOn={formValues.streamStatus}
          onToggleClick={onStreamStatusButtonsClick}
        />
      </Field>
    </>
  );

  const onSaveClick = () => {
    apiCall(() => saveMicrosoftDefenderConfigs({ ...formValues }))
      .then((res) => {
        showSuccessNotification({
          message: res,
        });
        setIsSaveClicked(true);
      })
      .catch(() => {
        showErrorNotification({
          message: "Error in saving data",
        });
      });
  };

  const onTestIntegrationClick = () => {
    apiCall(() => validateMicrosoftDefenderConfigs())
      .then(() => {
        showSuccessNotification({
          message: "Validation done successfully",
        });
        onCloseClick();
      })
      .catch((err: Error) => {
        showErrorNotification({
          message:
            (err as any)?.error || err.message || "Test Integration failed",
        });
      });
  };

  const isPreviousDataEqual = (
    formValues: FormValues,
    oktaConfigs: FormValues,
  ) => {
    const cloneFormValues = { ...formValues };

    return isEqual(cloneFormValues, oktaConfigs);
  };

  useEffect(() => {
    if (!isPreviousDataEqual(formValues, oktaConfigs)) {
      setIsSaveClicked(false);
    } else {
      setIsSaveClicked(true);
    }

    if (!formValues.eventHubInstance || !formValues.connectionString) {
      setIsSaveClicked(true);
    }
  }, [formValues, oktaConfigs]);

  const tableColumnConfig = useMemo(
    () => [...(tableConfig?.columns || [])],
    [tableConfig?.columns],
  );

  const renderCommonTable = () => (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={formValues?.contextSignals ?? []}
      pagination={{
        totalRecord: formValues?.contextSignals?.length,
        hasData: true,
        hasFetchedAllRecords: true,
        hasError: false,
        error: {},
        data: formValues?.contextSignals,
      }}
    />
  );

  const isSaveDisabled = () =>
    isSaveClicked ||
    !formValues?.eventHubInstance ||
    !formValues?.connectionString ||
    !formValues?.consumerGroup ||
    !formValues?.eventHubHost;

  const renderFooterSection = () => (
    <div style={{ display: "flex", gap: "20px" }}>
      <Button type="primary" onClick={onSaveClick} disabled={isSaveDisabled()}>
        {t("SAVE")}
      </Button>

      <Button
        type="secondary"
        onClick={onTestIntegrationClick}
        disabled={!isSaveClicked}
      >
        {t("TEST_INTEGRATION")}
      </Button>

      <Button
        type="tertiary"
        onClick={onCloseClick}
        containerStyle={{ paddingLeft: 0 }}
      >
        {t("CANCEL")}
      </Button>
    </div>
  );

  return (
    <Modal
      align="right"
      isBlocking={false}
      show={show}
      onEscape={onCloseClick}
      containerClass="crud-modal"
    >
      <ModalHeader text="EDIT_MICROSOFT_DEFENDER" onClose={onCloseClick} />
      <ModalBody>
        <>
          {renderBodySection()}
          {renderCommonTable()}
        </>
      </ModalBody>
      <ModalFooter>{renderFooterSection()}</ModalFooter>
    </Modal>
  );
}
