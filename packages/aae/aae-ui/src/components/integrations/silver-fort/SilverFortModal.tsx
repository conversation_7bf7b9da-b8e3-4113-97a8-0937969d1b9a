import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalHeader,
  Input,
  mergeFormValues,
  Field,
  Button,
  TableContainer,
  ToggleButton,
  PasswordInput,
} from "@zscaler/zui-component-library";
import { isEqual, noop } from "lodash-es";
import { useTranslation } from "react-i18next";
import { type FormValidationDetail } from "../../../types/modalhelper";
import { type Privileges } from "../../../types/permissions";
import { useGlobalContext } from "../../../ducks/global";
import { useIntegrations } from "../../../ducks/integrations";
import { useIntegrationsSelectors } from "../../../ducks/integrations/selectors";
import { useApiCall } from "../../../hooks/useApiCallContext";
import { getFormValidationDetail } from "./helper";

type SilverFortModalProps = {
  show: boolean;
  onCloseClick: () => void;
  privileges: Privileges;
};

type FormValues = {
  bearerToken?: string;
  ssfConfigEndpoint?: string;
  streamStatus?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  contextSignals?: any[];
};

export default function SilverFortModal({
  show,
  onCloseClick,
  privileges,
}: SilverFortModalProps) {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();
  const { hasFullAccess } = privileges;

  const {
    getSilverFortConfigs,
    saveSilverFortConfigs,
    validateSilverFortConfigs,
  } = useIntegrations();
  const { configsDetail: oktaConfigs, commonTableConfig: tableConfig } =
    useIntegrationsSelectors();

  const { showErrorNotification, showSuccessNotification } = useGlobalContext();

  const [formValues, setFormValues] = useState<FormValues>({});
  const [validationDetail, setValidationDetail] =
    useState<FormValidationDetail>({});
  const [isSaveClicked, setIsSaveClicked] = useState(false);

  useEffect(() => {
    // Fetch CrowdStrike configurations and update relevant form values
    apiCall(getSilverFortConfigs).catch(noop);
  }, [getSilverFortConfigs]);

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...oktaConfigs }));
  }, [oktaConfigs]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onFormFieldChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const formFiledData = mergeFormValues(evt) as FormValues;
    setFormValues(formFiledData);
  };

  const onStreamStatusButtonsClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      streamStatus: !prevState.streamStatus,
    }));
  };

  const renderBodySection = () => (
    <>
      <PasswordInput
        name="bearerToken"
        label="BEARER_TOKEN"
        onChange={onFormFieldChange}
        value={formValues?.bearerToken ?? ""}
        maxLength={256}
        canCopy
        disabled={!hasFullAccess}
        info={validationDetail}
        containerStyle={{ maxWidth: "35%" }}
      />

      <PasswordInput
        name="ssfConfigEndpoint"
        label="SSF_CONFIG_ENDPOINT"
        onChange={onFormFieldChange}
        value={formValues?.ssfConfigEndpoint ?? ""}
        maxLength={256}
        canCopy
        disabled={!hasFullAccess}
        info={validationDetail}
        containerStyle={{ maxWidth: "35%" }}
      />

      <Field label="STATUS">
        <ToggleButton
          type="success"
          showLabel={false}
          isOn={formValues.streamStatus}
          onToggleClick={onStreamStatusButtonsClick}
        />
      </Field>
    </>
  );

  const onSaveClick = () => {
    apiCall(() => saveSilverFortConfigs({ ...formValues }))
      .then((res) => {
        showSuccessNotification({
          message: res,
        });
        setIsSaveClicked(true);
      })
      .catch(() => {
        showErrorNotification({
          message: "Error in saving data",
        });
      });
  };

  const onTestIntegrationClick = () => {
    apiCall(() => validateSilverFortConfigs())
      .then(() => {
        showSuccessNotification({
          message: "Validation done successfully",
        });
        onCloseClick();
      })
      .catch((err: Error) => {
        showErrorNotification({
          message:
            (err as any)?.error || err.message || "Test Integration failed",
        });
      });
  };

  const isPreviousDataEqual = (
    formValues: FormValues,
    oktaConfigs: FormValues,
  ) => {
    const cloneFormValues = { ...formValues };

    return isEqual(cloneFormValues, oktaConfigs);
  };

  useEffect(() => {
    if (!isPreviousDataEqual(formValues, oktaConfigs)) {
      setIsSaveClicked(false);
    } else {
      setIsSaveClicked(true);
    }

    if (!formValues.bearerToken || !formValues.ssfConfigEndpoint) {
      setIsSaveClicked(true);
    }
  }, [formValues, oktaConfigs]);

  const tableColumnConfig = useMemo(
    () => [...(tableConfig?.columns || [])],
    [tableConfig?.columns],
  );

  const renderCommonTable = () => (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={formValues?.contextSignals ?? []}
      pagination={{
        totalRecord: formValues?.contextSignals?.length,
        hasData: true,
        hasFetchedAllRecords: true,
        hasError: false,
        error: {},
        data: formValues?.contextSignals,
      }}
    />
  );

  const isSaveDisabled = () =>
    isSaveClicked || !formValues?.bearerToken || !formValues?.ssfConfigEndpoint;

  const renderFooterSection = () => (
    <div style={{ display: "flex", gap: "20px" }}>
      <Button type="primary" onClick={onSaveClick} disabled={isSaveDisabled()}>
        {t("SAVE")}
      </Button>

      <Button
        type="secondary"
        onClick={onTestIntegrationClick}
        disabled={!isSaveClicked}
      >
        {t("TEST_INTEGRATION")}
      </Button>

      <Button
        type="tertiary"
        onClick={onCloseClick}
        containerStyle={{ paddingLeft: 0 }}
      >
        {t("CANCEL")}
      </Button>
    </div>
  );

  return (
    <Modal
      align="right"
      isBlocking={false}
      show={show}
      onEscape={onCloseClick}
      containerClass="crud-modal"
    >
      <ModalHeader text="EDIT_SILVERFORT" onClose={onCloseClick} />
      <ModalBody>
        <>
          {renderBodySection()}
          {renderCommonTable()}
        </>
      </ModalBody>
      <ModalFooter>{renderFooterSection()}</ModalFooter>
    </Modal>
  );
}
