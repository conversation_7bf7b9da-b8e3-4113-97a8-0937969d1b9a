import { defaultValidationDetail } from "@zscaler/zui-component-library";

import { FormValidationDetail } from "../../../types/modalhelper";

export type FormValues = {
  type?: string;
  bearerToken?: string;
  ssfConfigEndpoint?: string;
  streamStatus?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  contextSignals?: any[];
};

export const getFormValidationDetail = ({
  formValues,
}: {
  formValues: FormValues;
}): FormValidationDetail => {
  const validationDetail = { ...defaultValidationDetail };

  const { bearerToken, ssfConfigEndpoint } = formValues || {};

  if (!bearerToken) {
    validationDetail.isValid = false;
    validationDetail.context = "bearerToken";
    validationDetail.type = "error";
    validationDetail.message = "BEARER_TOKEN_REQUIRED_MESSAGE";

    return validationDetail;
  }

  if (!ssfConfigEndpoint) {
    validationDetail.isValid = false;
    validationDetail.context = "ssfConfigEndpoint";
    validationDetail.type = "error";
    validationDetail.message = "SSF_CONFIG_ENDPOINT_REQUIRED_MESSAGE";

    return validationDetail;
  }

  return validationDetail;
};
