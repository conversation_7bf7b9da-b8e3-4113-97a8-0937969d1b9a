import { type FormValues } from "../helper";

export type Operator = {
  symbol: string;
  name: string;
  valueType?: string;
};

export type Signal = {
  code: string;
  name: string;
  type?: string;
  valueType?: string;
  operators?: Operator[];
  values?: Array<string | { value: string; label: string }>;
  timeUnits?: string[];
  isCollection?: boolean;
  comparableSignals?: Array<{
    source: string;
    signal: string;
    signalName: string;
  }>;
};

export type DynamicField = {
  code: string;
  name: string;
  signalList?: Signal[];
};

export type Criterion = {
  field: string;
  subField?: string;
  operator?: string;
  value?: string;
  timeUnit?: string;
  compareSignalSource?: string;
  compareSignal?: string;
};

export type Group = {
  type: "AND" | "OR";
  criteria: Array<Criterion | Group>;
};

export type QueryBuilderProps = {
  formValues: FormValues;
  setFormValues: (values: FormValues) => void;
};

export type CriterionRowProps = {
  criterion: Criterion;
  onUpdate: (updatedCriterion: Criterion) => void;
  onDelete: () => void;
  fields: DynamicField[];
  level?: number;
};

export type CriteriaGroupProps = {
  group: Group;
  onUpdate: (updatedGroup: Group) => void;
  onDelete?: () => void;
  fields: DynamicField[];
  level?: number;
};

type ConditionCriteria = {
  field: string;
  subField: string;
  operator: string;
  value: string;
  timeUnit: string;
  compareSignalSource: string;
  compareSignal: string;
};

type ConditionJson = {
  type: "AND" | "OR";
  criteria: ConditionCriteria[];
};

type Profiles = {
  id: string;
  name: string;
  desc: string;
  conditions: string;
  conditionJson: ConditionJson | null;
  type: "USER" | "USER_DEVICE";
  profileState: string | null;
  tenantId: string;
  createdAt: number;
  modifiedAt: number;
  createdBy: string | null;
  modifiedBy: string | null;
};
