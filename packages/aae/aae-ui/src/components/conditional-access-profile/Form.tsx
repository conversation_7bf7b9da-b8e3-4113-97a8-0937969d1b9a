import type React from "react";
import { useContext, useEffect, useState } from "react";

import {
  Input,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
  Button,
  TextArea,
} from "@zscaler/zui-component-library";

import { cloneDeep } from "lodash-es";
import { CRUDPageContext } from "../../contexts/CRUDPageContextProvider";
import { type FormProps } from "../../types/modalhelper";
import { type FormValues, getFormValidationDetail } from "./helper";
import QueryBuilder from "./QueryBuilder";
import { useTranslation } from "react-i18next";

const Form: React.FC<FormProps> = ({
  validationDetail,
  setValidationDetail,
}) => {
  const { detail, setDetail, privileges } = useContext(CRUDPageContext)!;

  const { t } = useTranslation();

  type ValidateEmptyDetail = {
    conditions?: unknown;
    conditionJson?: unknown;
  };

  const validateEmptyDetail: ValidateEmptyDetail = cloneDeep(detail);

  const [formValues, setFormValues] = useState<FormValues>({
    name: "",
    desc: "",
    ...detail,
  });

  const { hasFullAccess } = privileges;

  const onFormFieldChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const formFiledData = mergeFormValues(evt) as FormValues;
    setFormValues(formFiledData);
  };

  useEffect(() => {
    setDetail({ ...formValues });

    const formValidationDetail = getFormValidationDetail({ formValues });

    setValidationDetail(formValidationDetail);
  }, [formValues, setDetail, setValidationDetail]);

  const renderGeneralSection = () => (
    <>
      <Input
        name="name"
        label="PROFILE_NAME"
        onChange={onFormFieldChange}
        value={formValues.name}
        disabled={!hasFullAccess}
        maxLength={128}
        placeHolder={t("ADD_PROFILE_PLACEHOLDER")}
        info={validationDetail}
      />
      <TextArea
        className="input"
        name="desc"
        label="DESCRIPTION_OPTIONAL"
        placeHolder={t("ADD_PROFILE_DESC_PLACEHOLDER")}
        onChange={onFormFieldChange}
        value={formValues.desc}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
      />
    </>
  );

  const renderInvalidData = () => {
    const handleReset = () => {
      validateEmptyDetail.conditions = "";
      validateEmptyDetail.conditionJson = {};

      setFormValues((prev) => ({
        ...prev,
        conditions: "",
        conditionJson: {},
      }));
    };

    return (
      <>
        <div className="expression-section">
          <h3 className="expression-title">Expression</h3>
          <pre className="expression mysql-expression">
            {formValues.conditions}
          </pre>
        </div>
        <div className="expression-section">
          <h3 className="expression-title">Criteria</h3>
          <pre
            style={{ color: "#ff5555" }}
            className="expression json-expression flex items-center"
          >
            Invalid JSON data <Button onClick={handleReset}>Reset</Button>
          </pre>
        </div>
      </>
    );
  };

  const isNotValidJson =
    typeof validateEmptyDetail.conditions === "string" &&
    validateEmptyDetail.conditions.trim() !== "" &&
    (validateEmptyDetail.conditionJson === null ||
      (typeof validateEmptyDetail.conditionJson === "object" &&
        validateEmptyDetail.conditionJson !== null &&
        Object.keys(validateEmptyDetail.conditionJson).length === 0));

  return (
    <section className="form-container">
      {renderGeneralSection()}
      {!isNotValidJson ? (
        <QueryBuilder formValues={formValues} setFormValues={setFormValues} />
      ) : (
        renderInvalidData()
      )}
    </section>
  );
};

export default Form;
