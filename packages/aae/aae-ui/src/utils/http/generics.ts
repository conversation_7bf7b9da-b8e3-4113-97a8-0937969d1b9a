import { type AxiosRequestConfig, type AxiosResponse } from "axios";
import http from "./http";

type GenericInterface<T> = {
  post: (obj: T, opts?: AxiosRequestConfig) => Promise<AxiosResponse>;
  read: (
    id?: string | number,
    opts?: AxiosRequestConfig,
  ) => Promise<AxiosResponse>;
  update: (
    obj: T & { id?: string | number },
    opts?: AxiosRequestConfig,
  ) => Promise<AxiosResponse>;
  del: (obj?: T, opts?: AxiosRequestConfig) => Promise<AxiosResponse>;
};

export const genericInterface = <T>(RESOURCE: string): GenericInterface<T> => ({
  post: async (obj: T, opts?: AxiosRequestConfig) =>
    http.post(RESOURCE, obj, opts),
  read: async (id?: string | number, opts?: AxiosRequestConfig) => {
    const url = id ? `${RESOURCE}/${id}` : RESOURCE;

    return http.get(url, opts);
  },
  update: async (
    obj: T & { id?: string | number },
    opts?: AxiosRequestConfig,
  ) => http.put(`${RESOURCE}/${obj?.id ?? ""}`, obj, opts),
  del: async (obj?: T, opts?: AxiosRequestConfig) =>
    http.delete(RESOURCE, { ...opts, data: obj }),
});
