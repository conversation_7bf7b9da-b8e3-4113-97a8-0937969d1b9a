.page-container {
  .table-container {
    height: calc(100vh - 216px);
  }
}

.truncated-text {
  display: inline-block;
  max-width: 30rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.crud-modal.right .modal-body-container {
  max-width: 836px;
  height: calc(100vh - 190px) !important;
}

.modal-container.right {
  margin-top: 3rem;
}

.toast-container {
  z-index: 99;
}

.configure-column-list-container {
  overflow: auto;
  .content-container {
    font-size: 0.875rem;
  }
}
.column-config-container > button > svg {
  height: 0.675em;
}
