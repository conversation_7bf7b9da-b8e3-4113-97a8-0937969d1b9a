export const APP_ID = "iam-context-engine";

export const PORTAL_ROOT_ID = "iam-context-engine-portal";

export const getAppID = () => "iam-context-engine";

export const getPortalID = () => "iam-context-engine-portal";

export const BASE_URL = "/private/zaa/adaptive-access";

// For development only
// export const BASE_URL = "https://oneui-test-1.oneui.zsloginalpha.net/api/adaptive-access";

export const HELP_PORTAL_URL = "https://help.zscaler.com/zslogin";

export const ZSCALER_FAQ = "https://www.zscaler.com/privacy/faq";

// Help portal query params setup
const origin = "admin.zslogin.net";

const zsLoginIndex = origin.indexOf("zslogin");

const referer =
  zsLoginIndex !== -1 ? origin.substring(zsLoginIndex) : "zslogin.net";

const queryParamsForHelp = `?source=admin-ui&referer=https://admin.${referer}`;

export const HELP_ARTICLES = {
  ADMINISTRATIVE_ENTITLEMENTS: `${HELP_PORTAL_URL}/about-administrative-entitlements${queryParamsForHelp}`,
  ADMINISTRATIVE_ENTITLEMENTS_ADMINISTRATIVE: `${HELP_PORTAL_URL}/about-administrative-entitlements-administrative${queryParamsForHelp}`,
  SERVICE_ENTITLEMENTS: `${HELP_PORTAL_URL}/about-service-entitlements${queryParamsForHelp}`,
  SERVICE_ENTITLEMENTS_SERVICE: `${HELP_PORTAL_URL}/about-service-entitlements-service${queryParamsForHelp}`,
  DASHBOARD: `${HELP_PORTAL_URL}/managing-your-user-profile-zslogin-admin-portal${queryParamsForHelp}`,
  SIGNON_POLICY: `${HELP_PORTAL_URL}/about-sign-policies${queryParamsForHelp}`,
  PASSWORD_POLICY: `${HELP_PORTAL_URL}/configuring-password-policy${queryParamsForHelp}`,
  USER_PROFILE: `${HELP_PORTAL_URL}/managing-your-user-profile-zslogin-admin-portal${queryParamsForHelp}`,
  USERS: `${HELP_PORTAL_URL}/about-users${queryParamsForHelp}`,
  USER_GROUPS: `${HELP_PORTAL_URL}/about-user-groups${queryParamsForHelp}`,
  PRIMARY_IDP_PROVIDER: `${HELP_PORTAL_URL}/about-primary-identity-provider${queryParamsForHelp}`,
  SECONDAY_IDP_PROVIDER: `${HELP_PORTAL_URL}/about-secondary-identity-providers${queryParamsForHelp}`,
  ADDING_SAML_IDENTITY: `${HELP_PORTAL_URL}/adding-saml-identity-providers${queryParamsForHelp}`,
  ADDING_OIDC_IDENTITY: `${HELP_PORTAL_URL}/adding-openid-providers${queryParamsForHelp}`,
  IP_LOCATIONS: `${HELP_PORTAL_URL}/about-ip-locations${queryParamsForHelp}`,
  IP_LOCATION_GROUPS: `${HELP_PORTAL_URL}/about-ip-location-groups${queryParamsForHelp}`,
  LINKED_SERVICES: `${HELP_PORTAL_URL}/about-linked-services${queryParamsForHelp}`,
  USER_ATTRIBUTES: `${HELP_PORTAL_URL}/about-attributes${queryParamsForHelp}`,
  ADVANCED_SETTINGS: `${HELP_PORTAL_URL}/configuring-advanced-settings${queryParamsForHelp}`,
  ZSCALER_SERVICES: `${HELP_PORTAL_URL}/about-zscaler-services${queryParamsForHelp}`,
  AUDIT_LOGS: `${HELP_PORTAL_URL}/about-audit-logs${queryParamsForHelp}`,
  MULTIFACTOR_SETTINGS: `${HELP_PORTAL_URL}/configuring-authentication-settings`,
  REMOTE_ASSISTANCE: `${HELP_PORTAL_URL}/enabling-remote-assistance`,
  DEVICE_TOKEN: `${HELP_PORTAL_URL}/managing-device-tokens`,
  BRANDING: `${HELP_PORTAL_URL}/customizing-branding`,
  SESSION_ATTRIBUTE: `${HELP_PORTAL_URL}/adding-session-attributes`,
  DEPARTMENTS: `${HELP_PORTAL_URL}/adding-departments`,
  DOMAINS: `${HELP_PORTAL_URL}/adding-guest-domains`,
  MANAGE_DEVICE_GROUPS: `${HELP_PORTAL_URL}/managing-device-groups${queryParamsForHelp}`,
} as const;

export const OTP_LETTERS_LIMIT = 6 as const;

// Define permission levels with explicit types
export const PERMISSION_LEVEL = {
  FULL: "full",
  RESTRICTED_FULL: "restrictedfull",
  VIEW: "view",
  RESTRICTED_VIEW: "restrictedview",
  NONE: "none",
} as const;

// Define permission keys with explicit types
export const PERMISSIONS_KEY = {
  ADMINISTRATIVE_ENTITLEMENTS_POLICY: "administrative-entitlement",
  AUDIT_LOGS_POLICY: "audit-Log",
  AUTHENTICATION_EVENT_LOG_POLICY: "authentication-event-log",
  AUTHENTICATION_METHODS: "authentication-methods",
  AUTHENTICATION_SESSION_POLICY: "authentication-session",
  BRANDING_POLICY: "branding",
  DEVICE_TOKEN: "device-token",
  GUEST_DOMAIN_POLICY: "guest-domain",
  EXTERNAL_IDENTITIES_POLICY: "external-identity",
  IP_LOCATION_POLICY: "ip-location",
  LINKED_TENATS_POLICY: "linked-tenant",
  REMOTE_ASSISTANCE_MANAGEMENT: "remote-assistance-management",
  ROLES_POLICY: "role",
  SERVICE_ENTITLEMENTS_POLICY: "service-entitlement",
  SIGN_ON_POLICY: "sign-on-policy",
  USERS_AND_GROUPS_POLICY: "user-and-group",
  USERS_CREDENTIALS_POLICY: "user-credential",
} as const;
