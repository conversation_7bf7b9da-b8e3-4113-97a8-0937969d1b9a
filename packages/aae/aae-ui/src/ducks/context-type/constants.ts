import {
  type TableColumnDetail,
  type TableConfig,
  type TableDetail,
} from "../../types/table";

export const REDUCER_KEY = "context-type";

export const API_ENDPOINT = "/admin/internal-api/v1/roles";

export const DATA_TABLE_DETAIL = "tableDetail";

export const DATA_INNER_TABLE_DETAIL = "innerTableDetail";

type RowData = {
  contextType?: string;
  source?: string;
  subjectCount?: string;
  overrideCount?: string;
  subjectIdentifier?: string;
  contextValue?: string;
  contextExpiry?: string;
  overrideValue?: string;
  overrideExpiry?: string;
};

const DEFAULT_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "contextType",
    accessorFn: (row: RowData) => row.contextType ?? "-",
    Header: "CONTEXT_TYPE",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "source",
    accessorFn: (row: RowData) => row.source ?? "-",
    Header: "SOURCE",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "subjectCount",
    accessorFn: (row: RowData) => row.subjectCount ?? "-",
    Header: "SUBJECT_COUNT",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "overrideCount",
    accessorFn: (row: RowData) => row.overrideCount ?? "-",
    Header: "OVERRIDE_COUNT",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
];

const DEFAULT_INNER_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "subjectIdentifier",
    accessorFn: (row: RowData) => row.subjectIdentifier ?? "-",
    Header: "SUBJECT_IDENTIFIER",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "contextValue",
    accessorFn: (row: RowData) => row.contextValue ?? "-",
    Header: "CONTEXT_VALUE",
    minSize: 120,
    size: 120,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "contextExpiry",
    accessorFn: (row: RowData) => row.contextExpiry ?? "-",
    Header: "CONTEXT_EXPIRY",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "overrideValue",
    accessorFn: (row: RowData) => row.overrideValue ?? "-",
    Header: "OVERRIDE_VALUE",
    minSize: 130,
    size: 130,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "overrideExpiry",
    accessorFn: (row: RowData) => row.overrideExpiry ?? "-",
    Header: "OVERRIDE_EXPIRY",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "actions",
    Header: "ACTIONS",
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: "alphanumeric",
    accessorFn: () => "-",
  },
];

export const DEFAULT_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_INNER_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_INNER_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL: TableDetail = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export type DefaultState = {
  [DATA_TABLE_DETAIL]: TableDetail;
  [DATA_INNER_TABLE_DETAIL]: TableDetail;
};

export const DEFAULT_STATE: DefaultState = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_INNER_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
