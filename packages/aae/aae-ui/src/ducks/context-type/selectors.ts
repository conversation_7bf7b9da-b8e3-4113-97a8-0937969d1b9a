import { useMemo } from "react";
import { useContextType } from "./index";
import {
  DATA_TABLE_DETAIL,
  DATA_INNER_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_INNER_TABLE_CONFIG,
} from "./constants";

export const useContextTypeSelectors = () => {
  const { state } = useContextType();

  const tableDetail = useMemo(() => state[DATA_TABLE_DETAIL], [state]);
  const innerTableDetail = useMemo(
    () => state[DATA_INNER_TABLE_DETAIL],
    [state],
  );
  const tableConfig = useMemo(() => DEFAULT_TABLE_CONFIG, []);
  const innerTableConfig = useMemo(() => DEFAULT_INNER_TABLE_CONFIG, []);

  return {
    tableDetail,
    innerTableDetail,
    tableConfig,
    innerTableConfig,
  };
};
