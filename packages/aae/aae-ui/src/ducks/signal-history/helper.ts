type CategoriesMapping = Record<string, string[]>;

type Mappings = {
  categories: string[];
  subCategories: string[];
  activeSubCategory: string[];
};

export const getCategoriesAndSubCategory = ({
  categoriesMapping = {},
  activeCategory = "ALL",
}: {
  categoriesMapping?: CategoriesMapping;
  activeCategory?: string;
}): Mappings => {
  const mappings: Mappings = {
    categories: [],
    subCategories: [],
    activeSubCategory: [],
  };

  Object.keys(categoriesMapping).forEach((category) => {
    mappings.categories.push(category);
    mappings.subCategories = [
      ...mappings.subCategories,
      ...categoriesMapping[category],
    ];
  });

  if (activeCategory === "ALL") {
    mappings.activeSubCategory = [...mappings.subCategories];
  } else {
    mappings.activeSubCategory = [...(categoriesMapping[activeCategory] || [])];
  }

  return mappings;
};
