"use client";

import type React from "react";
import {
  createContext,
  useContext,
  useReducer,
  useCallback,
  type ReactNode,
} from "react";
import dayjs from "dayjs";
import { isArray } from "lodash-es";
import { http } from "../../utils/http";
import type {
  GetListParams,
  UpdateTableDetailPayload,
} from "../../types/table";
import {
  API_ENDPOINT,
  DATA_CONTEXT_TYPES,
  DATA_SOURCES,
  DATA_SUBJECTS,
  DATA_TABLE_DETAIL,
  DEFAULT_CONTEXT_TYPES,
  DEFAULT_SOURCES,
  DEFAULT_STATE,
  DEFAULT_SUBJECTS,
  DEFAULT_TABLE_DETAIL,
} from "./constants";

// Types
type SignalHistoryState = typeof DEFAULT_STATE;

type SignalHistoryAction =
  | { type: "UPDATE_TABLE_DETAIL"; payload: UpdateTableDetailPayload }
  | { type: "UPDATE_SOURCES"; payload: unknown[] | undefined }
  | { type: "UPDATE_CONTEXT_TYPES"; payload: unknown[] | undefined }
  | { type: "UPDATE_SUBJECTS"; payload: unknown[] | undefined };

type SignalHistoryContextType = {
  state: SignalHistoryState;
  getList: (params: GetListParams) => Promise<void>;
  getSourcesEnums: () => Promise<void>;
  getContextTypesEnums: () => Promise<void>;
  getSubjectsEnums: () => Promise<void>;
};

// Context
const SignalHistoryContext = createContext<
  SignalHistoryContextType | undefined
>(undefined);

// Reducer
function signalHistoryReducer(
  state: SignalHistoryState,
  action: SignalHistoryAction,
): SignalHistoryState {
  switch (action.type) {
    case "UPDATE_TABLE_DETAIL": {
      const { result, pageSize, pageOffset, total } = action.payload;
      const tableDetail = { ...state[DATA_TABLE_DETAIL] };

      if (isArray(result)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;
        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;
        tableDetail.totalRecord = total;

        let totalResult: Array<Record<string, unknown>> = [];

        if (pageOffset) {
          totalResult = [...tableDetail.data, ...result];
        } else {
          totalResult = [...result];
        }

        tableDetail.data = totalResult;

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        return {
          ...state,
          [DATA_TABLE_DETAIL]: tableDetail,
        };
      }
      return state;
    }
    case "UPDATE_SOURCES":
      return {
        ...state,
        [DATA_SOURCES]: action.payload ?? DEFAULT_SOURCES,
      };
    case "UPDATE_CONTEXT_TYPES":
      return {
        ...state,
        [DATA_CONTEXT_TYPES]: action.payload ?? DEFAULT_CONTEXT_TYPES,
      };
    case "UPDATE_SUBJECTS":
      return {
        ...state,
        [DATA_SUBJECTS]: action.payload ?? DEFAULT_SUBJECTS,
      };
    default:
      return state;
  }
}

// Provider
export const SignalHistoryProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(signalHistoryReducer, DEFAULT_STATE);

  const getList = useCallback(
    async ({
      pageSize = DEFAULT_TABLE_DETAIL.pageSize,
      pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
      filters = {},
    }: GetListParams) => {
      const start = dayjs().startOf("day").valueOf();
      const end = dayjs().endOf("day").valueOf();

      const payload = {
        start,
        end,
        ...filters,
        sort: "time",
        order: "desc",
        limit: pageSize,
        next: pageOffset,
      };

      try {
        const response = await http.post(API_ENDPOINT, payload);

        if (response?.data) {
          const updatePayload: UpdateTableDetailPayload = {
            result: response?.data?.result,
            pageSize: pageSize,
            pageOffset: pageOffset,
            total: response?.data?.total,
          };

          dispatch({ type: "UPDATE_TABLE_DETAIL", payload: updatePayload });
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    },
    [],
  );

  const getSourcesEnums = useCallback(async () => {
    const requestUrl = "/catalog/sources";
    try {
      const response = await http.get(requestUrl);
      if (response?.data) {
        dispatch({ type: "UPDATE_SOURCES", payload: response?.data ?? [] });
      }
    } catch (error) {
      console.error("Error fetching sources:", error);
    }
  }, []);

  const getContextTypesEnums = useCallback(async () => {
    const requestUrl = "/catalog/context-types";
    try {
      const response = await http.get(requestUrl);
      if (response?.data) {
        dispatch({
          type: "UPDATE_CONTEXT_TYPES",
          payload: response.data ?? [],
        });
      }
    } catch (error) {
      console.error("Error fetching context types:", error);
    }
  }, []);

  const getSubjectsEnums = useCallback(async () => {
    const requestUrl = "/catalog/subjects";
    try {
      const response = await http.get(requestUrl);
      if (response?.data) {
        dispatch({ type: "UPDATE_SUBJECTS", payload: response.data ?? [] });
      }
    } catch (error) {
      console.error("Error fetching subjects:", error);
    }
  }, []);

  return (
    <SignalHistoryContext.Provider
      value={{
        state,
        getList,
        getSourcesEnums,
        getContextTypesEnums,
        getSubjectsEnums,
      }}
    >
      {children}
    </SignalHistoryContext.Provider>
  );
};

// Hook
export const useSignalHistory = () => {
  const context = useContext(SignalHistoryContext);
  if (context === undefined) {
    throw new Error(
      "useSignalHistory must be used within a SignalHistoryProvider",
    );
  }
  return context;
};
