"use client";

import React, {
  createContext,
  use<PERSON>allback,
  useReducer,
  useContext,
  type ReactNode,
} from "react";
import { isArray } from "lodash-es";
import { noop } from "lodash-es";
import { http } from "../../utils/http";
import {
  OKTA_API_ENDPOINT,
  CROWDSTRIKE_API_ENDPOINT,
  MICROSOFT_DEFENDER_API_ENDPOINT,
  UPDATE_LIST_ACTION_TYPE,
  UPDATE_CONFIGS_ACTION_TYPE,
  RESET_CONFIGS_ACTION_TYPE,
  DEFAULT_STATE,
  DATA_TABLE_DETAIL,
  CONFIGS_DETAIL,
  type DefaultState,
  SILVER_FORT_API_ENDPOINT,
} from "./constants";
import type { UpdateTableDetailPayload, TableDetail } from "../../types/table";
import { useFeatureFlags } from "../../zuxp-layout/FeatureFlagsProvider";

interface IntegrationConfig {
  name: string;
  isEnabled: boolean;
  endpoint: string;
  params?: Record<string, unknown>;
  transformResponse: (
    data: CrowdStrikeConfig | DefenderConfig,
  ) => IntegrationRecord;
}

interface IntegrationRecord extends Record<string, unknown> {
  name: string;
  contextType: string;
  streamStatus: boolean;
}

export type CrowdStrikeConfig = {
  webhookUrl?: string;
  jwksUrl?: string;
  baseUrls?: string[];
  contextSignals?: ContextSignal[];
  streamStatus?: boolean;
  status?: boolean;
  [key: string]: any;
};

export type ContextSignal = {
  name: string;
  typeUid: number;
  valueType: string;
  signalCode: string;
};

export type DefenderConfig = {
  type?: string;
  connectionString?: string;
  streamStatus?: boolean;
  eventHubInstance?: string;
  eventHubHost?: string;
  consumerGroup?: string;
  status?: boolean;
  contextSignals?: ContextSignal[];
};

export type SilverFortConfig = {
  type?: string;
  bearerToken?: string;
  streamStatus?: boolean;
  ssfConfigEndpoint?: string;
  status?: boolean;
  contextSignals?: ContextSignal[];
};

export type Configs = {
  data: CrowdStrikeConfig | DefenderConfig;
};

type SaveOktaConfigs = {
  data: string;
};

type IntegrationsAction =
  | { type: typeof UPDATE_LIST_ACTION_TYPE; payload: UpdateTableDetailPayload }
  | {
      type: typeof UPDATE_CONFIGS_ACTION_TYPE;
      payload: Record<string, unknown>;
    }
  | { type: typeof RESET_CONFIGS_ACTION_TYPE };

type IntegrationsContextType = {
  state: DefaultState;
  getList: () => Promise<void>;
  getOktaConfigs: () => Promise<void>;
  saveOktaConfigs: (payload: Record<string, unknown>) => Promise<string>;
  validateOktaConfigs: () => Promise<string>;
  generatePbks: (
    payload: Record<string, unknown>,
  ) => Promise<Record<string, string>>;
  getBaseUrlAndWebHookUrl: () => Promise<Record<string, unknown>>;
  getCrowdStrikeConfigs: () => Promise<void>;
  saveCrowdStrikeConfigs: (
    payload: Record<string, unknown>,
    isUpdate?: boolean,
  ) => Promise<string>;
  validateCrowdStrikeConfigs: (
    payload: Record<string, unknown>,
  ) => Promise<string>;
  getMicrosoftDefenderConfigs: () => Promise<void>;
  saveMicrosoftDefenderConfigs: (payload: DefenderConfig) => Promise<string>;
  validateMicrosoftDefenderConfigs: () => Promise<string>;
  getSilverFortConfigs: () => Promise<void>;
  saveSilverFortConfigs: (payload: DefenderConfig) => Promise<string>;
  validateSilverFortConfigs: () => Promise<string>;
  resetConfigs: () => void;
};

const IntegrationsContext = createContext<IntegrationsContextType | undefined>(
  undefined,
);

const integrationsReducer = (
  state: DefaultState,
  action: IntegrationsAction,
): DefaultState => {
  switch (action.type) {
    case UPDATE_LIST_ACTION_TYPE: {
      const tableDetail = { ...state[DATA_TABLE_DETAIL] } as TableDetail;
      const { records, pageSize, pageOffset, totalRecord } = action.payload;

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;
        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
        }
        tableDetail.data = [...records];

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }
      }

      return { ...state, [DATA_TABLE_DETAIL]: tableDetail };
    }
    case UPDATE_CONFIGS_ACTION_TYPE:
      return { ...state, [CONFIGS_DETAIL]: { ...action.payload } };
    case RESET_CONFIGS_ACTION_TYPE:
      return { ...state, [CONFIGS_DETAIL]: {} };
    default:
      return state;
  }
};

export const IntegrationsProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(integrationsReducer, DEFAULT_STATE);

  const { featureFlags } = useFeatureFlags();

  const getList = useCallback(async () => {
    try {
      const integrationsConfig: IntegrationConfig[] = [
        {
          name: "Okta",
          isEnabled: true,
          endpoint: `${OKTA_API_ENDPOINT}/configs`,
          transformResponse: (
            data: CrowdStrikeConfig | DefenderConfig,
          ): IntegrationRecord => ({
            name: "Okta",
            contextType: Array.isArray(data.contextSignals)
              ? data.contextSignals.map((signal) => signal.name).join(", ")
              : "No context signals available",
            streamStatus: !!data.streamStatus,
          }),
        },
        {
          name: "CrowdStrike",
          isEnabled: true,
          endpoint: `${CROWDSTRIKE_API_ENDPOINT}/configs`,
          transformResponse: (
            data: CrowdStrikeConfig | DefenderConfig,
          ): IntegrationRecord => ({
            name: "CrowdStrike",
            contextType: Array.isArray(data.contextSignals)
              ? data.contextSignals.map((signal) => signal.name).join(", ")
              : "No context signals available",
            streamStatus: !!data.status, // CrowdStrike-specific
          }),
        },
        {
          name: "Microsoft Defender",
          isEnabled: featureFlags.isAAEIntegrationEnabled,
          endpoint: `${MICROSOFT_DEFENDER_API_ENDPOINT}`,
          params: { connector: "DEFENDER" },
          transformResponse: (
            data: CrowdStrikeConfig | DefenderConfig,
          ): IntegrationRecord => ({
            name: "Microsoft Defender",
            contextType: Array.isArray(data.contextSignals)
              ? data.contextSignals.map((signal) => signal.name).join(", ")
              : "No context signals available",
            streamStatus: !!data.streamStatus,
          }),
        },
        {
          name: "Silverfort",
          isEnabled: featureFlags.isAAEIntegrationEnabled,
          endpoint: `${SILVER_FORT_API_ENDPOINT}`,
          params: { connector: "SILVERFORT" },
          transformResponse: (
            data: CrowdStrikeConfig | SilverFortConfig,
          ): IntegrationRecord => ({
            name: "Silverfort",
            contextType: Array.isArray(data.contextSignals)
              ? data.contextSignals.map((signal) => signal.name).join(", ")
              : "No context signals available",
            streamStatus: !!data.streamStatus,
          }),
        },
      ];

      const enabledIntegrations = integrationsConfig.filter(
        (integration) => integration.isEnabled,
      );

      const results = await Promise.allSettled(
        enabledIntegrations.map(({ endpoint, params, transformResponse }) =>
          http
            .get(endpoint, { params })
            .then((response) => transformResponse(response.data)),
        ),
      );

      const isFulfilled = (
        result: PromiseSettledResult<IntegrationRecord>,
      ): result is PromiseFulfilledResult<IntegrationRecord> =>
        result.status === "fulfilled";

      const records = results.filter(isFulfilled).map((result) => result.value);

      dispatch({
        type: UPDATE_LIST_ACTION_TYPE,
        payload: {
          records,
          totalRecord: records.length,
        },
      });
    } catch (error) {
      console.error("Error fetching list data:", error);
    }
  }, []);

  const getOktaConfigs = useCallback(async () => {
    const response: Configs = await http.get(`${OKTA_API_ENDPOINT}/configs`);
    if (response?.data) {
      dispatch({
        type: UPDATE_CONFIGS_ACTION_TYPE,
        payload: response?.data || {},
      });
    }
  }, []);

  const saveOktaConfigs = useCallback(
    async (payload: Record<string, unknown>): Promise<string> => {
      const response: SaveOktaConfigs = await http.post(
        `${OKTA_API_ENDPOINT}/configs`,
        payload,
      );

      if (response?.data) {
        await getOktaConfigs();
        await getList();
      }

      return response?.data;
    },
    [getOktaConfigs, getList],
  );

  const validateOktaConfigs = useCallback(async (): Promise<string> => {
    const response: SaveOktaConfigs = await http.get("/okta/validate");

    if (response?.data) {
      await getList().catch(noop);
    }

    return response?.data;
  }, [getList]);

  const generatePbks = useCallback(
    async (
      payload: Record<string, unknown>,
    ): Promise<Record<string, string>> => {
      const response = await http.post(`${OKTA_API_ENDPOINT}/jwks`, payload);
      return response.data;
    },
    [],
  );

  // crowdstrike
  const getBaseUrlAndWebHookUrl = useCallback(async (): Promise<any> => {
    const response = await http.get(`${CROWDSTRIKE_API_ENDPOINT}/webhook`);
    return response?.data;
  }, []);

  const getCrowdStrikeConfigs = useCallback(async () => {
    const response: Configs = await http.get(
      `${CROWDSTRIKE_API_ENDPOINT}/configs`,
    );

    if (response?.data) {
      dispatch({
        type: UPDATE_CONFIGS_ACTION_TYPE,
        payload: response?.data || {},
      });
    }
  }, []);

  const saveCrowdStrikeConfigs = useCallback(
    async (
      payload: Record<string, unknown>,
      isUpdate = false,
    ): Promise<string> => {
      delete payload.baseUrls;
      delete payload.webhookURL;

      let response: SaveOktaConfigs;

      if (isUpdate) {
        response = await http.put(
          `${CROWDSTRIKE_API_ENDPOINT}/configs`,
          payload,
        );
      } else {
        response = await http.post(
          `${CROWDSTRIKE_API_ENDPOINT}/configs`,
          payload,
        );
      }

      if (response?.data) {
        await getCrowdStrikeConfigs();
        await getList();
      }

      return response?.data;
    },
    [getCrowdStrikeConfigs, getList],
  );

  const validateCrowdStrikeConfigs = useCallback(
    async (payload: Record<string, unknown>): Promise<string> => {
      delete payload.baseUrls;
      delete payload.webhookURL;

      const response: SaveOktaConfigs = await http.post(
        `${CROWDSTRIKE_API_ENDPOINT}/validate`,
        payload,
      );

      if (response?.data) {
        await getList().catch(noop);
      }

      return response?.data;
    },
    [getList],
  );

  //microsoft defender
  const getMicrosoftDefenderConfigs = useCallback(async () => {
    const response: Configs = await http.get(
      `${MICROSOFT_DEFENDER_API_ENDPOINT}`,
      {
        params: {
          connector: "DEFENDER",
        },
      },
    );

    if (response?.data) {
      dispatch({
        type: UPDATE_CONFIGS_ACTION_TYPE,
        payload: response?.data || {},
      });
    }
  }, []);

  const saveMicrosoftDefenderConfigs = useCallback(
    async (payload: DefenderConfig): Promise<string> => {
      payload["type"] = "DEFENDER";
      const response: SaveOktaConfigs = await http.post(
        `${MICROSOFT_DEFENDER_API_ENDPOINT}`,
        payload,
      );

      if (response?.data) {
        await getMicrosoftDefenderConfigs();
        await getList();
      }

      return response?.data;
    },
    [getMicrosoftDefenderConfigs, getList],
  );

  const validateMicrosoftDefenderConfigs =
    useCallback(async (): Promise<string> => {
      const response: SaveOktaConfigs = await http.get(`/defender/validate`);

      if (response?.data) {
        await getList().catch(noop);
      }

      return response?.data;
    }, [getList]);

  //SILVERFORT
  const getSilverFortConfigs = useCallback(async () => {
    const response: Configs = await http.get(`${SILVER_FORT_API_ENDPOINT}`, {
      params: {
        connector: "SILVERFORT",
      },
    });

    if (response?.data) {
      dispatch({
        type: UPDATE_CONFIGS_ACTION_TYPE,
        payload: response?.data || {},
      });
    }
  }, []);

  const saveSilverFortConfigs = useCallback(
    async (payload: SilverFortConfig): Promise<string> => {
      payload["type"] = "SILVERFORT";
      const response: SaveOktaConfigs = await http.post(
        `${SILVER_FORT_API_ENDPOINT}`,
        payload,
      );

      if (response?.data) {
        await getSilverFortConfigs();
        await getList();
      }

      return response?.data;
    },
    [getSilverFortConfigs, getList],
  );

  const validateSilverFortConfigs = useCallback(async (): Promise<string> => {
    const response: SaveOktaConfigs = await http.get(`/silverfort/validate`);

    if (response?.data) {
      await getList().catch(noop);
    }

    return response?.data;
  }, [getList]);

  const resetConfigs = useCallback(() => {
    dispatch({ type: RESET_CONFIGS_ACTION_TYPE });
  }, []);

  return (
    <IntegrationsContext.Provider
      value={{
        state,
        getList,
        getOktaConfigs,
        saveOktaConfigs,
        validateOktaConfigs,
        generatePbks,
        getBaseUrlAndWebHookUrl,
        getCrowdStrikeConfigs,
        saveCrowdStrikeConfigs,
        validateCrowdStrikeConfigs,
        getMicrosoftDefenderConfigs,
        saveMicrosoftDefenderConfigs,
        validateMicrosoftDefenderConfigs,
        getSilverFortConfigs,
        saveSilverFortConfigs,
        validateSilverFortConfigs,
        resetConfigs,
      }}
    >
      {children}
    </IntegrationsContext.Provider>
  );
};

export const useIntegrations = (): IntegrationsContextType => {
  const context = useContext(IntegrationsContext);
  if (!context) {
    throw new Error(
      "useIntegrations must be used within an IntegrationsProvider",
    );
  }
  return context;
};
