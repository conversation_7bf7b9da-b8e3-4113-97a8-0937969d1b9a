import dayjs from "dayjs";
import {
  type TableColumnDetail,
  type TableConfig,
  type TableDetail,
} from "../../types/table";

export const REDUCER_KEY = "subject-identifier";

export const API_ENDPOINT = "/manager/v1/overrides";

export const DATA_TABLE_DETAIL = "tableDetail";

export const DATA_INNER_TABLE_DETAIL = "innerTableDetail";

type RowData = {
  contextType?: string;
  subjectName?: string;
  source?: string;
  subjectCount?: string;
  overrideCount?: string | null | undefined;
  subjectIdentifier?: string;
  contextValue?: number;
  contextExpiry?: number;
  overrideValue?: string;
  overrideExpiry?: number;
  contextCount?: string | null | undefined;
  subjectType?: string;
  contextLabel?: string;
  subjectLabel?: string;
};

const DEFAULT_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "subjectName",
    accessorFn: (row: RowData) => row.subjectName ?? "-",
    Header: "SUBJECT_IDENTIFIER",
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "subjectLabel",
    accessorFn: (row: RowData) => row.subjectLabel ?? "-",
    Header: "SUBJECT_TYPE",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "contextCount",
    accessorFn: (row: RowData) =>
      row.contextCount !== null || row.contextCount !== undefined
        ? row.contextCount
        : "-",
    Header: "CONTEXT_COUNT",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "overrideCount",
    accessorFn: (row: RowData) =>
      row.overrideCount !== null || row.overrideCount !== undefined
        ? row.overrideCount
        : "-",
    Header: "OVERRIDE_COUNT",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
];

const DEFAULT_INNER_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "contextLabel",
    accessorFn: (row: RowData) => row.contextLabel ?? "-",
    Header: "CONTEXT_TYPE",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "source",
    accessorFn: (row: RowData) => row.source ?? "-",
    Header: "SOURCE",
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "contextValue",
    accessorFn: (row: RowData) =>
      row.contextValue
        ? dayjs(Number(row.contextValue)).format("MMMM DD, YYYY - hh:mm A")
        : "-",
    Header: "CONTEXT_VALUE",
    minSize: 120,
    size: 120,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "overrideValue",
    accessorFn: (row: RowData) => row.overrideValue ?? "-",
    Header: "OVERRIDE_VALUE",
    minSize: 130,
    size: 130,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "overrideExpiry",
    accessorFn: (row: RowData) =>
      row.overrideExpiry
        ? dayjs(row.overrideExpiry * 1000).format("MMMM DD, YYYY - hh:mm A")
        : "-",
    Header: "OVERRIDE_EXPIRY",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "actions",
    Header: "ACTIONS",
    minSize: 80,
    size: 80,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: "alphanumeric",
    accessorFn: () => "-",
  },
];

export const DEFAULT_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_INNER_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_INNER_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL: TableDetail = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export type DefaultState = {
  [DATA_TABLE_DETAIL]: TableDetail;
  [DATA_INNER_TABLE_DETAIL]: TableDetail;
};

export const DEFAULT_STATE: DefaultState = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_INNER_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
