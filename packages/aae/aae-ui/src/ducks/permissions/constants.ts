import { PERMISSIONS_KEY } from "../../config";
import { type PermissionsKey } from "../../types/permissions";

export const REDUCER_KEY = "permissions";

export const API_ENDPOINT = "/admin/internal-api/v1/";

export const ALL_PERMISSIONS_API_ENDPOINT =
  "/admin/internal-api/v1/permissions";

export const MY_PERMISSIONS_API_ENDPOINT =
  "/admin/internal-api/v1/my-profile/permissions";

export const DATA_ALL_PERMISSIONS = "allPermissions";

export const DATA_ALL_PERMISSION_LEVELS = "allPermissionLevels";

export const DATA_MY_PERMISSIONS = "myPermissions";

export const DATA_MY_PERMISSIONS_LEVEL = "myPermissionsLevel";

export const DATA_ADMIN_ENTITLEMENT_PERMISSIONS = "adminEntitlementPermissions";

export const DEFAULT_ALL_PERMISSIONS: Array<{
  name: string;
  description: string;
}> = [];

export const DEFAULT_ALL_PERMISSION_LEVELS: Record<string, string[]> = {};

export const DEFAULT_MY_PERMISSIONS: string[] = [];

export const DEFAULT_MY_PERMISSIONS_LEVEL: Record<
  string,
  Record<string, boolean>
> = {};

export const DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS: string[] = [];

export const NONE_PERMISSION_BLACK_LIST: PermissionsKey[] = [
  PERMISSIONS_KEY.AUTHENTICATION_METHODS,
  PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY,
  PERMISSIONS_KEY.ROLES_POLICY,
];

export const DEFAULT_STATE = {
  [DATA_ALL_PERMISSIONS]: DEFAULT_ALL_PERMISSIONS,
  [DATA_ALL_PERMISSION_LEVELS]: DEFAULT_ALL_PERMISSION_LEVELS,
  [DATA_MY_PERMISSIONS]: DEFAULT_MY_PERMISSIONS,
  [DATA_MY_PERMISSIONS_LEVEL]: DEFAULT_MY_PERMISSIONS_LEVEL,
  [DATA_ADMIN_ENTITLEMENT_PERMISSIONS]: DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS,
};
