// setupWithContext.ts
import {
  API_METHOD,
  createDOMElement,
  isDOMElementPresent,
  setApiMethodMessageOption,
  setFloatingPortalRootId,
  setModalRootId,
  setToastPortalRootId,
  updateUseApiCallFunctionsRegistry,
} from "@zscaler/zui-component-library";

import { useGlobalContext } from "./index";
import { useApiHelpers } from "./apiHelpers";
import { APP_ID, PORTAL_ROOT_ID } from "../../config";

// This function remains unchanged as it doesn't depend on Redux
export const getAppRootElement = ({
  tagName = "div",
  parentElement = document.body,
  id = APP_ID,
}: {
  tagName?: string;
  parentElement?: HTMLElement;
  id?: string;
} = {}): HTMLElement | null => {
  const { isPresent } = isDOMElementPresent({ id });

  if (!isPresent) {
    createDOMElement({ tagName, parentElement, attributes: { id } });
  }

  const { element } = isDOMElementPresent({ id });
  return element;
};

// Custom hook to set up API call registry with context-based functions
export const useSetupFunctions = () => {
  const { showLoader, hideLoader, hideNotification } = useGlobalContext();
  const { apiSuccessNotifier, apiErrorNotifier } = useApiHelpers();

  const setupUseApiCallFunctionsRegistry = () => {
    updateUseApiCallFunctionsRegistry({
      showLoader,
      hideLoader,
      hideNotification,
      apiSuccessNotifier,
      apiErrorNotifier,
    });
  };

  // This function remains unchanged
  const setupModalRootId = ({ rootId }: { rootId?: string } = {}) => {
    setModalRootId(rootId ?? PORTAL_ROOT_ID);
  };

  // This function remains unchanged
  const setupFloatingPortalRootId = ({ rootId }: { rootId?: string } = {}) => {
    setFloatingPortalRootId(rootId ?? PORTAL_ROOT_ID);
    setupModalRootId({ rootId: rootId ?? PORTAL_ROOT_ID });
    setToastPortalRootId(rootId ?? PORTAL_ROOT_ID);
  };

  return {
    setupUseApiCallFunctionsRegistry,
    setupModalRootId,
    setupFloatingPortalRootId,
  };
};

export const setupApiMethodMessageOption = () => {
  setApiMethodMessageOption({
    [API_METHOD.GET]: { message: "", translationMapping: {} },
    [API_METHOD.PUT]: {
      message: "ALL_CHANGES_SAVED",
      translationMapping: {},
    },
    [API_METHOD.POST]: {
      message: "ALL_CHANGES_SAVED",
      translationMapping: {},
    },
    [API_METHOD.DELETE]: {
      message: "ITEM_HAS_BEEN_DELETED",
      translationMapping: {},
    },
  });
};
