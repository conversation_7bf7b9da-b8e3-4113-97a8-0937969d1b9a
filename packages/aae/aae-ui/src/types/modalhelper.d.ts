export type ModalMode = string;

export type ModalModeDetail = {
  [key in ModalMode]: {
    headerText?: string;
    confirmationMessage?: string;
  };
};

export type ActionOption = {
  label: string;
  value: string;
};

export type FormValidationDetail = {
  isValid?: boolean;
  context?: string;
  type?: string;
  message?: string;
};

export type FormProps = {
  mode?: string;
  validationDetail?: FormValidationDetail;
  setValidationDetail: React.Dispatch<
    React.SetStateAction<FormValidationDetail>
  >;
};

export type PermissionLevelDetail = {
  id: string;
  label: string;
  order: number;
};

export type PermissionLevelDetailType = Record<string, PermissionLevelDetail>;

export type PermissionKeyDetail = {
  label: string;
  tooltip: React.ReactNode;
  defaultPermission?: string;
};

export type PermissionKeyDetailType = Record<string, PermissionKeyDetail>;

export type TooltipDetail = {
  content: React.ReactNode;
};
