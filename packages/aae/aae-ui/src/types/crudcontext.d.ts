import { type ReactNode } from "react";
import { type Privileges } from "./permissions";

export type BulkActionOption = {
  label: string;
  value: string;
};

export type CsvImportResultDetail = Record<string, unknown>;

export type UseCRUDPageContextProps = {
  defaultModalMode?: string | boolean;
  defaultDetail?: Record<string, unknown>;
  defaultBulkActionOption?: BulkActionOption;
  defaultSearchOption?: Record<string, unknown>;
  defaultCsvImportDetail?: Record<string, unknown>;
  defaultCsvImportResultDetail?: CsvImportResultDetail | null;
  privileges?: Privileges;
};

export type CRUDPageContextProviderProps = {
  children: ReactNode;
} & UseCRUDPageContextProps;

export type CRUDPageContextValue = {
  modalMode: string | boolean;
  setModalMode: React.Dispatch<React.SetStateAction<string | boolean>>; // Updated type
  isFormReadOnly: boolean;
  setIsFormReadOnly: React.Dispatch<React.SetStateAction<boolean>>;
  detail: Record<string, unknown>;
  setDetail: React.Dispatch<React.SetStateAction<Record<string, unknown>>>;
  selectedRowDetail: unknown[];
  setSelectedRowDetail: React.Dispatch<React.SetStateAction<unknown[]>>;
  selectedBulkAction: BulkActionOption;
  setSelectedBulkAction: React.Dispatch<React.SetStateAction<BulkActionOption>>;
  selectedSearchField: Record<string, unknown>;
  setSelectedSearchField: React.Dispatch<
    React.SetStateAction<Record<string, unknown>>
  >;
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  csvImportDetail: Record<string, unknown>;
  setCsvImportDetail: React.Dispatch<
    React.SetStateAction<Record<string, unknown>>
  >;
  csvImportResult: CsvImportResultDetail | null;
  setCsvImportResult: React.Dispatch<
    React.SetStateAction<CsvImportResultDetail | null>
  >;
  defaultModalMode: string | boolean;
  defaultDetail: Record<string, unknown>;
  defaultBulkActionOption: BulkActionOption;
  defaultSearchOption: Record<string, unknown>;
  defaultCsvImportDetail: Record<string, unknown>;
  defaultCsvImportResultDetail: CsvImportResultDetail | null;
  privileges: Privileges;
};
