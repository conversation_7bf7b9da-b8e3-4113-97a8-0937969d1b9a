/* eslint-disable @typescript-eslint/no-explicit-any */
declare module "@zscaler/zui-component-library" {
  import { type ComponentType } from "react";

  // import { type AppDispatch } from "../store";

  // Define a catch-all for any component
  type AnyComponent = ComponentType<any>;

  // Define a generic function type
  type GenericFunction = (...args: any[]) => any;

  // Define the shape of the module
  type ZUIComponentLibrary = {
    // Components
    Button: AnyComponent;
    DropDown: AnyComponent;
    Search: AnyComponent;
    TableContainer: AnyComponent;
    TableCellContainer: GenericFunction;
    TextWithTooltip: AnyComponent;
    CalendarDropDown: AnyComponent;
    Field: AnyComponent;
    FieldGroup: AnyComponent;
    Modal: AnyComponent;
    Tooltip: AnyComponent;
    Icon: AnyComponent;
    Loader: AnyComponent;
    Notification: AnyComponent;
    Tabs: AnyComponent;
    TabPanel: AnyComponent;
    Form: AnyComponent;
    Input: AnyComponent;
    TextArea: AnyComponent;
    Checkbox: AnyComponent;
    RadioButton: AnyComponent;
    Select: AnyComponent;
    Switch: AnyComponent;
    Pagination: AnyComponent;
    Slider: AnyComponent;
    Table: AnyComponent;
    Accordion: AnyComponent;
    Card: AnyComponent;
    List: AnyComponent;
    ListItem: AnyComponent;
    LoaderContainer: AnyComponent;
    ToastContainer: AnyComponent;
    Actions: AnyComponent;
    ModalBody: AnyComponent;
    ModalFooter: AnyComponent;
    ModalHeader: AnyComponent;
    ToggleButton: AnyComponent;
    PasswordInput: AnyComponent;
    CRUDModal: AnyComponent;
    InlineDatePicker: AnyComponent;
    Tab: AnyComponent;

    // Functions
    getCalendarDDList: GenericFunction;
    getDropDownList: GenericFunction;
    getTimeRange: GenericFunction;
    useDropDownActions: GenericFunction;
    setApiMethodMessageOption: GenericFunction;
    createDOMElement: GenericFunction;
    isDOMElementPresent: GenericFunction;
    setModalRootId: GenericFunction;
    setFloatingPortalRootId: GenericFunction;
    setToastPortalRootId: GenericFunction;
    updateUseApiCallFunctionsRegistry: GenericFunction;
    openModal: GenericFunction;
    closeModal: GenericFunction;
    showToast: GenericFunction;
    hideToast: GenericFunction;
    getErrorMessageFromApiResponse: GenericFunction;
    mergeFormValues: GenericFunction;
    getApiDELETENotificationOptions: GenericFunction;
    getApiPOSTNotificationOptions: GenericFunction;
    getApiPUTNotificationOptions: GenericFunction;

    // useApiCall hook
    // useApiCall: (options?: {
    //   dispatch?: AppDispatch;
    //   hasLoader?: boolean;
    //   hasNotification?: boolean;
    //   clearNotification?: boolean;
    //   clearNotificationOnNextCall?: boolean;
    //   clearLoader?: boolean;
    //   errorNotificationPayload?: Record<string, unknown>;
    //   successNotificationPayload?: Record<string, unknown>;
    // }) => {
    //   apiCall: <T>(
    //     apiCall: (dispatch: AppDispatch) => Promise<T>, // Supports Redux thunks
    //     config?: Record<string, unknown>,
    //   ) => Promise<T>;
    //   cleanupNotification: (id?: string) => void;
    //   cleanupLoader: () => void;
    // };

    // API_METHOD as generic key-value pair
    API_METHOD: Record<string, string>;

    // Allow any other named export to be a component or function
    [key: string]: AnyComponent | GenericFunction;

    defaultValidationDetail: {
      isValid: boolean;
      context: string;
      type: "error" | "warning" | "info";
      message: string;
    };
    defaultFormProps: Record<string, any>;
    defaultFormPropTypes: Record<string, any>;
  };

  // Export the entire module as matching this interface
  const zui: ZUIComponentLibrary;
  export = zui;
}
