export type CalendarConfig = {
  minDate: dayjs.Dayjs;
  maxInterval: number;
  message: { text: string };
};

// Types for individual filters and the selected filter
export type TimeRange = {
  startTime?: number;
  endTime?: number;
  label?: string;
  value?: string;
  isRangeSame?: boolean;
  displayText?: string;
};

export type FilterOption = {
  label: string;
  value: string;
};

export type DropDownOnSelection = {
  label: string;
  value: string;
};

export type DropDownActionsResult = {
  isDropDownLoading: boolean;
  onDropDownOpen: () => void;
};
