# @aae/pages

This package is a **dependency manager** for `@aae/aae-ui`. It contains no actual code and exists solely to manage the version of `@aae/aae-ui` and any peer dependencies.

## Purpose

This package is intentionally empty and serves only as a dependency manager for `@aae/aae-ui`. This approach:

1. Eliminates all unnecessary code and complexity
2. Allows direct imports from `@aae/aae-ui` in the Next.js app
3. Maintains CODEOWNERS access over this package.json file for easier version updates
4. Provides a clean way to manage transitive dependencies

## Usage

In your Next.js pages, import the components directly from `@aae/aae-ui`:

```tsx
"use client";

// Importing directly from @aae/aae-ui instead of @aae/pages
export { IntegrationsPage as default } from "@aae/aae-ui";
```

The app can import from `@aae/aae-ui` even if the app's package.json only pins `@aae/pages`, because `@aae/aae-ui` is available as a transitive dependency through this package.

## Development

This package requires no development as it contains no actual code. It simply serves as a dependency manager.
