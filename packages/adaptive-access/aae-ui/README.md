# Experience Center (XC) - common

Generated: Fri Feb 14 2025 08:51:57 GMT-0700 (Mountain Standard Time) by [@xc/cli]()
Template : component-library v0.0.1

Common XC focused components and utilities


## Relevant Commands

| Command                   | Description                                                |
| ------------------------- | ---------------------------------------------------------- |
| pnpm build                | build package                                              |
| pnpm test                 | run package unit tests                                     |
| pnpm test:ui              | run package unit tests with ui                             |
| pnpm test:watch           | run package unit tests in watch mode                       |
| pnpm prettier             | run prettier on this package and fix any issues            |
| pnpm prettier:check       | run prettier on this package                               |
| pnpm lint                 | run eslint on this package                                 |
| pnpm lint:fix             | run eslint on this package and apply automatic fixes       |
| pnpm verify               | runs both prettier:check and lint on this package          |
| pnpm storybook            | run storybook for this packag                              |
| pnpm build-storybook      | build the storybook for this package                       |
| pnpm build-storybook-test | quick builds the storybook for this package                |

