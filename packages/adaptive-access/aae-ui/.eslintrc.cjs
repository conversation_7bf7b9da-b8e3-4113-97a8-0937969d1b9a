/** @type {import("eslint").Linter.Config} */
module.exports = {
  root: true,
  extends: ["@up/eslint-config/component-library.js"],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    project: "./tsconfig.json",
    tsconfigRootDir: __dirname,
  },
  ignorePatterns: [
    "src/**/*.data.ts",
    "src/**/*.test.ts",
    "src/**/*.spec.ts",
    ".eslint*",
    "tailwind.config.ts",
    "!.storybook",
    "storybook-static"
  ],
};
