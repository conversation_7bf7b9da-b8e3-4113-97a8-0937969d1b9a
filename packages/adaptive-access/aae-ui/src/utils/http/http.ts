"use client";

import axios, { type AxiosInstance } from "axios";
import { getBearerToken } from "@up/std";
import { BASE_URL } from "../../config";
import { handleError } from "./handleError";

export type XCEnvironment = {
  endpoint(): string;
};

export type CloudServiceMapping = {
  cloudName: string;
  tenantId: string;
  ziaCloudName?: string;
};

export type CloudConfig = {
  getCloudList(): { ziam: CloudServiceMapping[] }; // Adjusted type for cloud list
};

// Internal references
let environment: XCEnvironment | undefined;
let getCloudList: (() => { ziam: Array<{ tenantId: string }> }) | undefined;

// Function to initialize the HTTP client by injecting dependencies
export function initializeHttpClient(
  env: XCEnvironment,
  cloudListGetter: CloudConfig,
): void {
  environment = env;
  getCloudList = () => ({
    ziam: cloudListGetter.getCloudList().ziam || [], // Transform cloud list
  });
}

const http: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 3 * 60 * 1000, // Increased to support image download (usually 1 - 2 GB)
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

http.interceptors.request.use((config) => {
  if (!environment || !getCloudList) {
    throw new Error(
      "HTTP client is not initialized. Call initializeHttpClient before making requests.",
    );
  }

  const tenantId = getCloudList().ziam[0]?.tenantId;
  const publicAPIEndpoint = environment.endpoint();
  const accessToken = getBearerToken();

  config.headers.Authorization = `Bearer ${accessToken}`;
  config.headers["x-zscaler-tenant-id"] = tenantId;

  config.baseURL = `${publicAPIEndpoint}${config.baseURL}`;
  return config;
});

http.interceptors.response.use((response) => response, handleError);

export default http;
