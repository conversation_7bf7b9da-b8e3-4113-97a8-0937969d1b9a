import React from "react";
import ErrorBoundaryWrapper from "../components/error-boundary/ErrorBoundaryWrapper";
import IntegrationsPage from "../pages/IntegrationsPage";
import { ResponsiveContainer } from "../components/common/ResponsiveContainer";
import { IntegrationsProvider } from "../ducks/integrations";

// Simple error fallback component
const ErrorFallback = () => (
  <div>
    <h1>Oops! Something went wrong.</h1>
    <p>We are sorry for the inconvenience. Please try again later.</p>
  </div>
);

export default function IntegrationsPageWrapper() {
  return (
    <ErrorBoundaryWrapper fallback={() => <ErrorFallback />}>
      <IntegrationsProvider>
        <ResponsiveContainer id="integration-page">
          <IntegrationsPage />
        </ResponsiveContainer>
      </IntegrationsProvider>
    </ErrorBoundaryWrapper>
  );
}
