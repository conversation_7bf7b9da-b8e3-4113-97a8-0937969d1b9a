import React from "react";
import ErrorBoundaryWrapper from "../components/error-boundary/ErrorBoundaryWrapper";
import ProfilesPage from "../pages/ProfilesPage";
import { ResponsiveContainer } from "../components/common/ResponsiveContainer";
import { ProfileProvider } from "../ducks/conditional-access-profile";

// Simple error fallback component
const ErrorFallback = () => (
  <div>
    <h1>Oops! Something went wrong.</h1>
    <p>We are sorry for the inconvenience. Please try again later.</p>
  </div>
);

export default function ProfilesPageWrapper() {
  return (
    <ErrorBoundaryWrapper fallback={() => <ErrorFallback />}>
      <ProfileProvider>
        <ResponsiveContainer id="profiles-page">
          <ProfilesPage />
        </ResponsiveContainer>
      </ProfileProvider>
    </ErrorBoundaryWrapper>
  );
}
