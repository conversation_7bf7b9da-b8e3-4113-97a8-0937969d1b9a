import React from "react";
import ErrorBoundaryWrapper from "../components/error-boundary/ErrorBoundaryWrapper";
import OverrideManagerPage from "../pages/OverrideManagerPage";
import { ResponsiveContainer } from "../components/common/ResponsiveContainer";
import { SubjectIdentifierProvider } from "../ducks/subject-identifier";
import { ContextTypeProvider } from "../ducks/context-type";

// Simple error fallback component
const ErrorFallback = () => (
  <div>
    <h1>Oops! Something went wrong.</h1>
    <p>We are sorry for the inconvenience. Please try again later.</p>
  </div>
);

export default function OverrideManagerPageWrapper() {
  return (
    <ErrorBoundaryWrapper fallback={() => <ErrorFallback />}>
      <SubjectIdentifierProvider>
        <ContextTypeProvider>
          <ResponsiveContainer id="overrides-page">
            <OverrideManagerPage />
          </ResponsiveContainer>
        </ContextTypeProvider>
      </SubjectIdentifierProvider>
    </ErrorBoundaryWrapper>
  );
}
