import React from "react";
import ErrorBoundaryWrapper from "../components/error-boundary/ErrorBoundaryWrapper";
import SignalHistoryPage from "../pages/SignalHistoryPage";
import { ResponsiveContainer } from "../components/common/ResponsiveContainer";
import { SignalHistoryProvider } from "../ducks/signal-history";

// Simple error fallback component
const ErrorFallback = () => (
  <div>
    <h1>Oops! Something went wrong.</h1>
    <p>We are sorry for the inconvenience. Please try again later.</p>
  </div>
);

export default function SignalHistoryPageWrapper() {
  return (
    <ErrorBoundaryWrapper fallback={() => <ErrorFallback />}>
      <SignalHistoryProvider>
        <ResponsiveContainer id="signal-history-page">
          <SignalHistoryPage />
        </ResponsiveContainer>
      </SignalHistoryProvider>
    </ErrorBoundaryWrapper>
  );
}
