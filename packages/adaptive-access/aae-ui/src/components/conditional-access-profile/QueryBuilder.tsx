import type React from "react";
import {
  useState,
  useCallback,
  useEffect,
  useMemo,
  useContext,
  useRef,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash, faCopy, faPaste } from "@fortawesome/pro-regular-svg-icons";
import { convertTimeToMilliseconds, type FormValues } from "./helper";
import {
  type QueryBuilderProps,
  type Group,
  type Criterion,
  type DynamicField,
  type Signal,
  type CriterionRowProps,
  type CriteriaGroupProps,
  type Operator,
} from "./types/query-builder";
import { useProfileSelectors } from "../../ducks/conditional-access-profile/selectors";
import { Button, DropDown } from "@zscaler/zui-component-library";
import { CRUDPageContext } from "../../contexts/CRUDPageContextProvider";
import { cloneDeep, isEqual } from "lodash-es";

const addOptions = [
  { label: "Add Criterion", value: "criterion" },
  { label: "Add Subgroup", value: "subgroup" },
];

const QueryBuilder: React.FC<QueryBuilderProps> = ({
  formValues,
  setFormValues,
}) => {
  const { dynamicFields } = useProfileSelectors();

  const { modalMode } = useContext(CRUDPageContext)!;

  const initialFormValuesRef = useRef<FormValues | null>(null);

  useEffect(() => {
    if (!initialFormValuesRef.current && modalMode) {
      // Store a deep clone without the dynamic properties we don't want to compare
      const cleanedFormValues = cloneDeep(formValues);
      delete cleanedFormValues.isSaveEnabled;
      delete cleanedFormValues.conditions;

      initialFormValuesRef.current = cleanedFormValues;
    }
  }, [formValues, modalMode]);

  const hasFormValuesChanged = useMemo(() => {
    if (!initialFormValuesRef.current) return false;

    const currentCleanValues = cloneDeep(formValues);
    delete currentCleanValues.isSaveEnabled;
    delete currentCleanValues.conditions;

    return !isEqual(currentCleanValues, initialFormValuesRef.current);
  }, [formValues]);

  const [rootGroup, setRootGroup] = useState<Group>(() => {
    try {
      const pastedGroup = JSON.parse(formValues?.conditionJson ?? "") as Group;

      return pastedGroup && typeof pastedGroup === "object"
        ? pastedGroup
        : { type: "OR", criteria: [] };
    } catch (error) {
      console.error("Failed to parse content: ", error);

      return { type: "OR", criteria: [] };
    }
  });

  const areAllFieldsFilled = useMemo(() => {
    const isAllFieldsFilled = (criteria: Array<Criterion | Group>): boolean =>
      criteria.every((criterion) => {
        if ((criterion as Group).type) {
          return isAllFieldsFilled((criterion as Group).criteria);
        } else {
          const field = dynamicFields.find(
            (f) => f.code === (criterion as Criterion).field,
          );
          const subField = field?.signalList?.find(
            (sf) =>
              sf.code.toLowerCase() ===
              (criterion as Criterion).subField?.toLowerCase(),
          );
          const currentField = subField ?? field?.signalList?.[0];
          const isSignal = (
            field: Signal | DynamicField | undefined,
          ): field is Signal => (field as Signal)?.valueType !== undefined;

          const selectedOperator = isSignal(currentField)
            ? currentField?.operators?.find(
                (op) => op.symbol === (criterion as Criterion).operator,
              )
            : undefined;

          if (!currentField) return false;

          const isFieldFilled = (criterion as Criterion).field !== "";
          const isSubFieldFilled = field?.signalList
            ? (criterion as Criterion).subField !== ""
            : true;

          switch (selectedOperator?.valueType ?? currentField?.valueType) {
            case "event":
              return isFieldFilled && isSubFieldFilled;
            case "Number":
              return (
                isFieldFilled &&
                isSubFieldFilled &&
                (criterion as Criterion).operator !== "" &&
                (criterion as Criterion).value !== ""
              );
            case "TimeRange":
              return (
                isFieldFilled &&
                isSubFieldFilled &&
                (criterion as Criterion).operator !== "" &&
                (criterion as Criterion).value !== "" &&
                (criterion as Criterion).timeUnit !== ""
              );
            case "Signal":
              return (
                isFieldFilled &&
                isSubFieldFilled &&
                (criterion as Criterion).operator !== "" &&
                (criterion as Criterion).compareSignalSource !== "" &&
                (criterion as Criterion).compareSignal !== ""
              );
            case "String":
              if (currentField.values && currentField.values.length > 0) {
                return (
                  isFieldFilled &&
                  isSubFieldFilled &&
                  (criterion as Criterion).operator !== "" &&
                  (criterion as Criterion).value !== ""
                );
              }

              // For String without predefined values
              return (
                isFieldFilled &&
                isSubFieldFilled &&
                (criterion as Criterion).value !== ""
              );
            case "Boolean":
              return (
                isFieldFilled &&
                isSubFieldFilled &&
                (criterion as Criterion).operator !== "" &&
                (criterion as Criterion).timeUnit !== ""
              );
            default:
              if (currentField.isCollection) {
                return (
                  isFieldFilled &&
                  isSubFieldFilled &&
                  (criterion as Criterion).operator !== "" &&
                  (criterion as Criterion).value !== "" &&
                  (currentField.timeUnits
                    ? (criterion as Criterion).timeUnit !== ""
                    : true)
                );
              }

              // For any other unhandled types
              return (
                isFieldFilled &&
                isSubFieldFilled &&
                (criterion as Criterion).operator !== "" &&
                (criterion as Criterion).value !== ""
              );
          }
        }
      });

    return (
      rootGroup.criteria.length > 0 && isAllFieldsFilled(rootGroup.criteria)
    );
  }, [rootGroup, dynamicFields]);

  const [copyStatus, setCopyStatus] = useState<string>("");
  const [pasteStatus, setPasteStatus] = useState<string>("");

  const generateMySQLExpression = useMemo(
    () =>
      (group: Group, dynamicFields: DynamicField[]): string => {
        if (!group.criteria || group.criteria.length === 0) return "";

        const conditions = group.criteria.map((criterion) => {
          if ((criterion as Group).type) {
            // Recursive call for subgroups, ensuring subgroups are always wrapped in parentheses
            return `(${generateMySQLExpression(criterion as Group, dynamicFields)})`;
          } else {
            // Resolve selected field and subfield for single conditions
            const selectedField = dynamicFields.find(
              (f) => f.code === (criterion as Criterion).field,
            );
            const subFields = selectedField?.signalList ?? [];
            const selectedSubField = subFields.find(
              (sf) =>
                sf.code === (criterion as Criterion).subField ||
                sf.name === (criterion as Criterion).subField,
            );

            const field = selectedSubField
              ? `sources.${(criterion as Criterion).field}.${selectedSubField.code}`
              : `sources.${(criterion as Criterion).field}`;

            const value = (criterion as Criterion).value;
            const operator = (criterion as Criterion).operator ?? "";

            const currentField = selectedSubField ?? selectedField;

            // Helper function to check if field is a `Signal`
            const isSignal = (
              field: Signal | DynamicField | undefined,
            ): field is Signal => (field as Signal)?.valueType !== undefined;

            const selectedOperator = isSignal(currentField)
              ? currentField.operators?.find(
                  (op: Operator) => op.symbol === operator,
                )
              : undefined;

            // Generate condition string based on the type
            if (isSignal(currentField)) {
              if (currentField.valueType === "event") {
                return `(${field} = 'true')`;
              } else if (currentField.isCollection) {
                return (
                  `(${field} contains value ${operator} '${value ?? ""}' ${(criterion as Criterion).timeUnit ?? ""}`.trim() +
                  ")"
                );
              } else if (selectedOperator?.valueType === "TimeRange") {
                const currentTimestamp = Date.now();
                const timeInMilliseconds = convertTimeToMilliseconds(
                  Number.parseInt(value ?? "0"),
                  (criterion as Criterion).timeUnit ?? "",
                );
                const pastTimestamp = currentTimestamp - timeInMilliseconds;

                return `(${field} >= ${pastTimestamp} AND ${field} <= ${currentTimestamp})`.trim();
              } else if (selectedOperator?.valueType === "Signal") {
                return `(${field} ${operator} ${
                  (criterion as Criterion).compareSignal &&
                  (criterion as Criterion).compareSignalSource
                    ? "${sources." +
                      `${(criterion as Criterion).compareSignalSource}.${(criterion as Criterion).compareSignal}` +
                      "}"
                    : ""
                })`.trim();
              } else {
                const valueString =
                  currentField.valueType === "Number" ? value : `'${value}'`;
                return `(${field} ${operator} ${valueString})`.trim();
              }
            } else {
              const valueString =
                isSignal(currentField) && currentField.valueType === "Number"
                  ? value
                  : `'${value}'`;

              return `(${field} ${operator} ${valueString})`.trim();
            }
          }
        });

        // Join the conditions with group type (AND/OR)
        const groupExpression = conditions.join(` ${group.type} `);

        // Wrap group conditions with parentheses
        return `(${groupExpression})`;
      },
    [],
  );

  const mysqlExpression = generateMySQLExpression(rootGroup, dynamicFields);

  type InputType = {
    value: string;
    label: string;
  };

  const renderNumberInput = useCallback(
    (
      currentField: Signal,
      criterion: Criterion,
      handleChange: (key: string, value: string) => void,
    ) => (
      <>
        {currentField.values && currentField.values.length > 0 ? (
          <select
            value={criterion.value}
            onChange={(e) => handleChange("value", e.target.value)}
            className="criteria-select"
          >
            <option value="">Select Value</option>
            {currentField.values.map((value: InputType | string) =>
              typeof value === "object" ? (
                <option key={value.value ?? ""} value={value.value}>
                  {value.label}
                </option>
              ) : (
                <option key={value} value={value}>
                  {value}
                </option>
              ),
            )}
          </select>
        ) : (
          <input
            type="number"
            value={criterion.value}
            onChange={(e) => {
              if (
                Number.parseInt(e.target.value) > 0 &&
                Number.parseInt(e.target.value) <= 100
              ) {
                handleChange("value", e.target.value.toString());
              }
            }}
            min="1"
            max="100"
            className="criteria-input"
          />
        )}

        {currentField.timeUnits && currentField.timeUnits.length > 0 && (
          <select
            value={criterion.timeUnit}
            onChange={(e) => handleChange("timeUnit", e.target.value)}
            className="criteria-select"
          >
            <option value="">Select Time Unit</option>
            {currentField.timeUnits.map((unit) => (
              <option key={unit} value={unit}>
                {unit}
              </option>
            ))}
          </select>
        )}
      </>
    ),
    [],
  );

  const renderStringInput = useCallback(
    (
      currentField: Signal,
      criterion: Criterion,
      handleChange: (key: string, value: string) => void,
    ) => {
      if (currentField.values && currentField.values.length > 0) {
        return (
          <select
            value={criterion.value}
            onChange={(e) => handleChange("value", e.target.value)}
            className="criteria-select"
          >
            <option value="">Select Value</option>
            {currentField.values.map((value: InputType | string) =>
              typeof value === "object" ? (
                <option key={value.value} value={value.value}>
                  {value.label}
                </option>
              ) : (
                <option key={value} value={value}>
                  {value}
                </option>
              ),
            )}
          </select>
        );
      }

      return (
        <input
          type="text"
          value={criterion.value}
          onChange={(e) => handleChange("value", e.target.value)}
          className="criteria-input"
          placeholder="Value"
        />
      );
    },
    [],
  );

  const renderSignalInput = useCallback(
    (
      currentField: Signal,
      criterion: Criterion,
      handleChange: (key: string, value: string) => void,
    ) => (
      <>
        <select
          value={criterion.compareSignalSource}
          onChange={(e) => {
            handleChange("compareSignalSource", e.target.value);
          }}
          className="criteria-select"
        >
          <option value="">Select Source</option>
          {Array.from(
            new Set(
              currentField.comparableSignals?.map((signal) => signal.source) ??
                [],
            ),
          ).map((source) => (
            <option key={source} value={source}>
              {source}
            </option>
          ))}
        </select>
        {criterion.compareSignalSource && (
          <select
            value={criterion.compareSignal}
            onChange={(e) => handleChange("compareSignal", e.target.value)}
            className="criteria-select"
          >
            <option value="">Select Signal</option>
            {currentField.comparableSignals
              ?.filter(
                (signal) => signal.source === criterion.compareSignalSource,
              )
              .map((signal) => (
                <option key={signal.signal} value={signal.signal}>
                  {signal.signalName}
                </option>
              ))}
          </select>
        )}
      </>
    ),
    [],
  );

  const renderInput = useCallback(
    (
      currentField: Signal,
      criterion: Criterion,
      handleChange: (key: string, value: string) => void,
    ) => {
      const selectedOperator = currentField.operators?.find(
        (op) => op.symbol === criterion.operator,
      );

      switch (selectedOperator?.valueType ?? currentField.valueType) {
        case "String":
          return renderStringInput(currentField, criterion, handleChange);
        case "Number":
          return renderNumberInput(currentField, criterion, handleChange);
        case "TimeRange":
          return renderNumberInput(currentField, criterion, handleChange);
        case "Signal":
          return renderSignalInput(currentField, criterion, handleChange);
        default:
          return null;
      }
    },
    [renderStringInput, renderNumberInput, renderSignalInput],
  );

  const CriterionRow: React.FC<CriterionRowProps> = useCallback(
    ({ criterion, onUpdate, onDelete, fields, level = 0 }) => {
      const handleChange = (key: string, value: string) => {
        let updatedCriterion = { ...criterion, [key]: value };
        if (key === "field") {
          updatedCriterion = {
            ...updatedCriterion,
            subField: "",
            operator: "",
            value: "",
            timeUnit: "",
            compareSignalSource: "",
            compareSignal: "",
          };
        } else if (key === "subField") {
          updatedCriterion = {
            ...updatedCriterion,
            operator: "",
            value: "",
            timeUnit: "",
            compareSignalSource: "",
            compareSignal: "",
          };
        } else if (key === "operator") {
          updatedCriterion = {
            ...updatedCriterion,
            value: "",
            timeUnit: "",
            compareSignalSource: "",
            compareSignal: "",
          };
        } else if (key === "compareSignalSource") {
          updatedCriterion = { ...updatedCriterion, compareSignal: "" };
        }

        onUpdate(updatedCriterion);
      };

      const selectedField = fields.find((f) => f.code === criterion.field);
      const subFields = selectedField?.signalList ?? [];

      const selectedSubField = subFields.find(
        (sf) =>
          sf.code === criterion.subField || sf.name === criterion.subField,
      );

      const currentField = selectedSubField ?? selectedField;
      const isSignal = (
        field: Signal | DynamicField | undefined,
      ): field is Signal => (field as Signal)?.valueType !== undefined;

      return (
        <div className="criteria-row">
          <select
            value={criterion.field}
            onChange={(e) => handleChange("field", e.target.value)}
            className="criteria-select"
          >
            <option value="">Select Field</option>
            {fields.map((field) => (
              <option key={field.code} value={field.code}>
                {field.name}
              </option>
            ))}
          </select>

          {subFields.length > 0 && (
            <select
              value={criterion.subField}
              onChange={(e) => handleChange("subField", e.target.value)}
              className="criteria-select"
            >
              <option value="">Select Sub-Field</option>
              {subFields.map((subField) => (
                <option
                  key={`${subField.code}-${subField.name}`}
                  value={subField.name}
                >
                  {subField.name}
                </option>
              ))}
            </select>
          )}

          {isSignal(currentField) &&
            currentField.operators &&
            currentField.operators.length > 0 &&
            currentField.valueType !== "event" && (
              <select
                value={criterion.operator}
                onChange={(e) => handleChange("operator", e.target.value)}
                className="criteria-select"
              >
                <option value="">Select Operator</option>
                {currentField.operators.map((op: Operator) => (
                  <option key={op.symbol} value={op.symbol}>
                    {op.name}
                  </option>
                ))}
              </select>
            )}

          {isSignal(currentField) &&
            criterion.operator &&
            currentField.valueType !== "event" &&
            renderInput(currentField, criterion, handleChange)}
          <button
            onClick={onDelete}
            className="delete-button"
            title="Delete Criterion"
          >
            <FontAwesomeIcon icon={faTrash} />
          </button>
        </div>
      );
    },
    [renderInput],
  );

  const CriteriaGroup: React.FC<CriteriaGroupProps> = useCallback(
    ({ group, onUpdate, onDelete, fields, level = 0 }) => {
      const addCriterion = () => {
        onUpdate({
          ...group,
          criteria: [
            ...(group.criteria || []),
            { field: "", subField: "", operator: "", value: "" },
          ],
        });
      };

      const updateCriterion = (
        index: number,
        updatedCriterion: Criterion | Group,
      ) => {
        const newCriteria = [...(group.criteria || [])];
        newCriteria[index] = updatedCriterion;
        onUpdate({ ...group, criteria: newCriteria });
      };

      const deleteCriterion = (index: number) => {
        const newCriteria = (group.criteria || []).filter(
          (_, i) => i !== index,
        );
        onUpdate({ ...group, criteria: newCriteria });
      };

      const addSubGroup = () => {
        onUpdate({
          ...group,
          criteria: [...(group.criteria || []), { type: "OR", criteria: [] }],
        });
      };

      const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const newType = e.target.value === "AND" ? "AND" : "OR";
        onUpdate({ ...group, type: newType });
      };

      return (
        <div className="criteria-group">
          <div className="group-header">
            <select
              value={group.type}
              onChange={handleTypeChange}
              className="group-select"
            >
              <option value="AND">ALL</option>
              <option value="OR">ANY</option>
            </select>
            {level > 0 && onDelete && (
              <button
                onClick={onDelete}
                className="delete-group-button"
                title="Delete Group"
              >
                <FontAwesomeIcon icon={faTrash} />
              </button>
            )}
          </div>
          <div className="criteria-items">
            {(group.criteria || []).map((criterion, index) => (
              <div key={index} className="criteria-item">
                {(criterion as Group).type ? (
                  <CriteriaGroup
                    group={criterion as Group}
                    onUpdate={(updatedGroup) =>
                      updateCriterion(index, updatedGroup)
                    }
                    onDelete={() => deleteCriterion(index)}
                    fields={fields}
                    level={level + 1}
                  />
                ) : (
                  <CriterionRow
                    criterion={criterion as Criterion}
                    onUpdate={(updatedCriterion) =>
                      updateCriterion(index, updatedCriterion)
                    }
                    onDelete={() => deleteCriterion(index)}
                    fields={fields}
                    level={level + 1}
                  />
                )}
              </div>
            ))}
          </div>

          <div className="add-select-container">
            <DropDown
              list={addOptions}
              selectedList={[{ label: "ADD", value: "ADD" }]}
              onSelection={(payload: typeof addOptions) => {
                if (payload[0]?.value === "criterion") {
                  addCriterion();
                } else if (payload[0]?.value === "subgroup") {
                  addSubGroup();
                }
              }}
              selectedItemsProps={{
                kind: "tertiary",
                containerStyle: {
                  justifyContent: "center",
                  paddingLeft: "0",
                  paddingRight: "0",
                  minWidth: "70px",
                  border: " 0px",
                  background: "none",
                },
              }}
              itemsSelectionProps={{
                containerStyle: {
                  minWidth: "130px",
                },
              }}
            />
          </div>
        </div>
      );
    },
    [CriterionRow],
  );

  useEffect(() => {
    const determineType = (group: Group): string => {
      let hasUserDevice = false;
      const checkCriteria = (criteria: Array<Criterion | Group>): boolean => {
        for (const criterion of criteria) {
          if ((criterion as Group).type) {
            if (checkCriteria((criterion as Group).criteria)) {
              hasUserDevice = true;
            }
          } else {
            const field = dynamicFields.find(
              (f) => f.code === (criterion as Criterion).field,
            );
            const subField = field?.signalList?.find(
              (sf) => sf.code === (criterion as Criterion).subField,
            );
            if (
              subField?.type === "USER_DEVICE" ||
              field?.signalList?.some((sf) => sf.type === "USER_DEVICE")
            ) {
              hasUserDevice = true;
            }
          }
          if (hasUserDevice) break;
        }

        return hasUserDevice;
      };

      return checkCriteria(group.criteria) ? "USER_DEVICE" : "USER";
    };

    setFormValues((prevState: FormValues) => ({
      ...prevState,
      conditions: generateMySQLExpression(rootGroup, dynamicFields),
      conditionJson: JSON.stringify(rootGroup, null, 2),
      type: determineType(rootGroup),
      // Only enable save when there are changes AND all fields are filled
      isSaveEnabled: hasFormValuesChanged && areAllFieldsFilled,
    }));
  }, [
    rootGroup,
    dynamicFields,
    setFormValues,
    generateMySQLExpression,
    hasFormValuesChanged,
    areAllFieldsFilled,
  ]);

  const handleCopy = useCallback(() => {
    const jsonString = JSON.stringify(rootGroup);
    navigator.clipboard
      .writeText(jsonString)
      .then(() => {
        setCopyStatus("Cloned Profile");
        setTimeout(() => setCopyStatus(""), 2000);
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
        setCopyStatus("Copy failed");
        setTimeout(() => setCopyStatus(""), 2000);
      });
  }, [rootGroup]);

  const handlePaste = useCallback(() => {
    navigator.clipboard
      .readText()
      .then((text: string) => {
        try {
          const pastedGroup = JSON.parse(text) as Group;
          if (pastedGroup && typeof pastedGroup === "object") {
            setRootGroup(pastedGroup);
            setPasteStatus("Cloned Profile successfully!");
            setTimeout(() => setPasteStatus(""), 2000);
          } else {
            throw new Error("Invalid data structure");
          }
        } catch (error) {
          console.error("Failed to parse pasted content: ", error);
          setPasteStatus("Invalid data");
          setTimeout(() => setPasteStatus(""), 2000);
        }
      })
      .catch((err) => {
        console.error("Failed to paste: ", err);
        setPasteStatus("Paste failed");
        setTimeout(() => setPasteStatus(""), 2000);
      });
  }, []);

  return (
    <div className="dynamic-criteria-builder">
      <div className="flex justify-between">
        <div className="text-sm font-medium">Criteria</div>
        <div className="action-buttons">
          <Button onClick={handleCopy} className="btn">
            <FontAwesomeIcon icon={faCopy} /> &nbsp; Copy
          </Button>
          <Button onClick={handlePaste} className="btn">
            <FontAwesomeIcon icon={faPaste} /> &nbsp; Paste
          </Button>
          {copyStatus && (
            <span className="status copy-status">{copyStatus}</span>
          )}
          {pasteStatus && (
            <span className="status paste-status">{pasteStatus}</span>
          )}
        </div>
      </div>
      <div style={{ overflow: "auto", marginBottom: "1rem" }}>
        <CriteriaGroup
          group={rootGroup}
          onUpdate={setRootGroup}
          fields={dynamicFields}
          level={0}
        />
      </div>
      <div className="expression-section">
        <div className="text-xs font-medium">Expression</div>
        <pre className="expression mysql-expression">{mysqlExpression}</pre>
      </div>
    </div>
  );
};

export default QueryBuilder;
