"use client";

import { ReactNode, useEffect, useRef, useState } from "react";
import { getAppID } from "../../config";

interface ResponsiveContainerProps {
  id: string;
  className?: string;
  children: ReactNode;
}

export function ResponsiveContainer({ id, className = "page-padding", children }: ResponsiveContainerProps) {
  const containerRef = useRef<HTMLElement | null>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  useEffect(() => {
    const containerElement = document.getElementById(getAppID());
    containerRef.current = containerElement;

    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    };

    const resizeObserver = new ResizeObserver(() => {
      updateWidth();
    });

    if (containerElement) {
      resizeObserver.observe(containerElement);
      updateWidth(); // Initial width update
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [id]);

  return (
    <article
      id={id}
      className={className}
      ref={containerRef}
      style={{ width: `${containerWidth}px` }}
    >
      {children}
    </article>
  );
}