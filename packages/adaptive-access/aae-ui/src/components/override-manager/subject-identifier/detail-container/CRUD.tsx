import type React from "react";
import { useContext } from "react";
import {
  getApiPUTNotificationOptions,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
} from "@zscaler/zui-component-library";

import NoAccess from "../../../no-access/NoAccess";
import { CRUDPageContext } from "../../../../contexts/CRUDPageContextProvider";
import Form from "./Form";
import { useGlobalContext } from "../../../../ducks/global";
import {
  TypePayload,
  useSubjectIdentifierContext,
} from "../../../../ducks/subject-identifier";
import { useApiCall } from "../../../../hooks/useApiCallContext";

const CRUD: React.FC = () => {
  const { apiCall } = useApiCall();
  const { showErrorNotification } = useGlobalContext();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultModalMode,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext)!;

  const { updateRow } = useSubjectIdentifierContext();

  const { hasFullAccess, noAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
  };

  const onSaveClick = () => {
    if (modalMode === "edit") {
      const updatedDetail: TypePayload = { ...detail };

      apiCall(() => updateRow(updatedDetail), {
        successNotificationPayload: getApiPUTNotificationOptions(),
      })
        .then(() => {
          onCloseClick();
        })
        .catch((err: Error) => {
          showErrorNotification({
            message: err.message || "Error in update",
          });
        });
    }
  };

  if (noAccess) {
    return <NoAccess />;
  }

  const showSave = hasFullAccess && !detail.isSystemRole;
  const cancelText = hasFullAccess && !detail.isSystemRole ? "CANCEL" : "CLOSE";

  return (
    modalMode && (
      <Modal
        align="right"
        isBlocking={false}
        show={modalMode}
        onEscape={onCloseClick}
        containerClass="crud-modal override-manager"
      >
        <ModalHeader text={detail.subjectIdentifier} onClose={onCloseClick} />
        <ModalBody>
          <Form />
        </ModalBody>
        <ModalFooter
          showSave={showSave}
          cancelText={cancelText}
          onSave={onSaveClick}
          onCancel={onCloseClick}
        />
      </Modal>
    )
  );
};

export default CRUD;
