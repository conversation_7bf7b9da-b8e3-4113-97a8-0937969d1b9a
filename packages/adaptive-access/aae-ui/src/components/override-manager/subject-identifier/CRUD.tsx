import { useContext } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@zscaler/zui-component-library";

import { noop } from "lodash-es";
import NoAccess from "../../no-access/NoAccess";

import { CRUDPageContext } from "../../../contexts/CRUDPageContextProvider";
import Form from "./Form";
import { useSubjectIdentifierContext } from "../../../ducks/subject-identifier";
import { useApiCall } from "../../../hooks/useApiCallContext";

const CRUD = () => {
  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext)!;

  const { apiCall } = useApiCall();
  const { getInnerList } = useSubjectIdentifierContext();

  const { noAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(false);
    setDetail(defaultDetail);
    apiCall(() => getInnerList({ isEmpty: true })).catch(noop);
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    modalMode && (
      <Modal
        align="right"
        isBlocking={false}
        show={modalMode}
        onEscape={onCloseClick}
        containerClass="crud-modal override-manager"
      >
        <ModalHeader text={detail.subjectIdentifier} onClose={onCloseClick} />
        <ModalBody>
          <Form />
        </ModalBody>
      </Modal>
    )
  );
};

export default CRUD;
