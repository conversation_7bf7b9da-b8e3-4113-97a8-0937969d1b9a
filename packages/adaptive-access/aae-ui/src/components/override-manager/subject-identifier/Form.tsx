import { useContext, useEffect, useRef, useState } from "react";

import {
  defaultFormPropTypes,
  defaultFormProps,
  TextWithTooltip,
} from "@zscaler/zui-component-library";

import { noop } from "lodash-es";
import CRUDPageContextProvider, {
  CRUDPageContext,
} from "../../../contexts/CRUDPageContextProvider";
import { type Privileges } from "../../../types/permissions";
import Table from "./detail-container/Table";
import CRUD from "./detail-container/CRUD";
import {
  GetInnerListParams,
  useSubjectIdentifierContext,
} from "../../../ducks/subject-identifier";
import { useApiCall } from "../../../hooks/useApiCallContext";

const defaultProps = {
  ...defaultFormProps,
};

// Define the expected type of the detail object
type Detail = {
  subjectType?: string;
  subjectId?: string;
  subjectName?: string;
  contextCount?: number;
  overrideCount?: number;
};

const Form = () => {
  const { detail, setDetail, privileges } = useContext(CRUDPageContext) as {
    detail: Detail;
    setDetail: (detail: Detail) => void;
    privileges: Privileges;
  };

  const { getInnerList } = useSubjectIdentifierContext();

  const { apiCall } = useApiCall();

  const [formValues] = useState<Detail>({
    ...detail,
  });

  const isInitialized = useRef(false);

  useEffect(() => {
    if (!isInitialized.current) {
      const params: GetInnerListParams = {
        subjectType: detail?.subjectType,
        subjectId: detail?.subjectId,
      };

      apiCall(() => getInnerList(params)).catch(noop);
      isInitialized.current = true;

      setTimeout(() => {
        isInitialized.current = false;
      }, 500);
    }
  }, [detail]);

  useEffect(() => {
    setDetail({ ...formValues });
  }, [formValues, setDetail]);

  const renderGeneralSection = () => (
    <>
      <TextWithTooltip
        containerStyle={{ fontSize: "1rem" }}
        text={detail?.subjectName ?? ""}
      />
      <TextWithTooltip
        containerStyle={{
          marginTop: "8px",
          marginBottom: "8px",
          fontSize: "0.8rem",
        }}
        text={`${detail?.contextCount ?? 0} Contexts, ${
          detail?.overrideCount ?? 0
        } Override`}
      />
      <CRUDPageContextProvider defaultDetail={detail} privileges={privileges}>
        <Table />
        <CRUD />
      </CRUDPageContextProvider>
    </>
  );

  return <section className="form-container">{renderGeneralSection()}</section>;
};

Form.defaultProps = defaultProps;

Form.propTypes = { ...defaultFormPropTypes };

export default Form;
