import { defaultValidationDetail } from "@zscaler/zui-component-library";
import { PERMISSIONS_KEY, PERMISSION_LEVEL } from "../../../config";
import {
  type ActionOption,
  type FormValidationDetail,
  type ModalModeDetail,
  type PermissionKeyDetailType,
  type PermissionLevelDetailType,
  type TooltipDetail,
} from "../../../types/modalhelper";

export const modalModeDetail: ModalModeDetail = {
  "": {},
  view: {
    headerText: "SUBJECT_IDENTIFIER",
  },
  add: {
    headerText: "SUBJECT_IDENTIFIER",
  },
  edit: {
    headerText: "SUBJECT_IDENTIFIER",
  },
  delete: {
    headerText: "DELETE_COMMON_CRITERIA_PROFILE",
    confirmationMessage:
      "Are you sure you want to delete this Profile? The changes cannot be undone.",
  },
  bulkDelete: {
    headerText: "BULK_DELETE",
    confirmationMessage: "BULK_DELETE_ROLE_CONFIRMATION_MESSAGE",
  },
  importFile: {
    headerText: "IMPORT_ROLE",
  },
};

export const bulkActionOptions: ActionOption[] = [
  { label: "DELETE", value: "DELETE" },
];

export const defaultBulkActionOption: ActionOption = {
  label: "ACTIONS",
  value: "ACTIONS",
};

export const PERMISSIONS_LEVEL_DETAIL: PermissionLevelDetailType = {
  [PERMISSION_LEVEL.FULL]: {
    id: PERMISSION_LEVEL.FULL,
    label: "Full",
    order: 1,
  },
  [PERMISSION_LEVEL.RESTRICTED_FULL]: {
    id: PERMISSION_LEVEL.RESTRICTED_FULL,
    label: "Restricted Full",
    order: 2,
  },
  [PERMISSION_LEVEL.VIEW]: {
    id: PERMISSION_LEVEL.VIEW,
    label: "View Only",
    order: 3,
  },
  [PERMISSION_LEVEL.RESTRICTED_VIEW]: {
    id: PERMISSION_LEVEL.RESTRICTED_VIEW,
    label: "Restricted View",
    order: 4,
  },
  [PERMISSION_LEVEL.NONE]: {
    id: PERMISSION_LEVEL.NONE,
    label: "None",
    order: 5,
  },
};

type FormValues = {
  name?: string;
};

export const getFormValidationDetail = ({
  formValues,
}: {
  formValues: FormValues;
}): FormValidationDetail => {
  const validationDetail = { ...defaultValidationDetail };

  const { name } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = "name";
    validationDetail.type = "error";
    validationDetail.message = "NAME_REQUIRED_MESSAGE";

    return validationDetail;
  }

  return validationDetail;
};

export const PERMISSION_KEY_DETAILS: PermissionKeyDetailType = {
  [PERMISSIONS_KEY.SIGN_ON_POLICY]: {
    label: "SIGN_ON_POLICIES",
    tooltip: (
      <p>
        Set the access level to{" "}
        <strong className="tooltip-bold">Policy &gt; Admin Sign-On</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_METHODS]: {
    label: "AUTHENTICATION_METHODS",
    tooltip: (
      <p>
        Set the access level to{" "}
        <strong className="tooltip-bold">Policy &gt; Password</strong> and
        <strong className="tooltip-bold">
          Administration &gt; Authentication Methods
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  // ... (other permission key details follow the same structure)
};

export const getFormTooltipDetail = (name: string): TooltipDetail => {
  const tooltipDetail: TooltipDetail = {
    content: "",
  };

  switch (name) {
    case "name":
      tooltipDetail.content = `Enter a name for the location`;
      break;
    case "country":
      tooltipDetail.content = `Select the country of the IP location`;
      break;
    case "ipAddress":
      tooltipDetail.content = (
        <p>
          Enter the IP address of the location and click{" "}
          <strong className="tooltip-bold">Add Items</strong>. You can add
          multiple IP addresses for a location. Click{" "}
          <strong className="tooltip-bold">X</strong> to remove an IP address or{" "}
          <strong className="tooltip-bold">Remove All</strong> to remove all
          selections.
        </p>
      );
      break;
    case "overrideExistingEntries":
      tooltipDetail.content = (
        <p>
          Enable this option if you want to update your existing locations,
          delete existing locations, or add new locations. If you only want to
          add new locations, Zscaler doesn&apos;t recommend selecting this
          option. To learn more, see{" "}
          <a href="https://help.zscaler.com/zslogin/importing-ip-locations-csv-file">
            Importing IP Locations from a CSV File
          </a>
          .
        </p>
      );
      break;
    case "csvFile":
      tooltipDetail.content = (
        <p>
          Click <strong className="tooltip-bold">Browse File</strong> and select
          the CSV file you want to import.
        </p>
      );
      break;
  }

  return tooltipDetail;
};
