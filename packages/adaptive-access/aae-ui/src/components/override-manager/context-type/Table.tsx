/** eslint-disable @typescript-eslint/no-explicit-any */
import type React from "react";
import { useCallback, useContext, useMemo } from "react";
import { TableContainer } from "@zscaler/zui-component-library";

import { noop } from "lodash-es";

import { CRUDPageContext } from "../../../contexts/CRUDPageContextProvider";
import { type TableColumnDetail } from "../../../types/table";
import { useContextTypeSelectors } from "../../../ducks/context-type/selectors";
import { useContextType } from "../../../ducks/context-type";
import { useApiCall } from "../../../hooks/useApiCallContext";

type RowData = {
  contextType?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
};

const Table: React.FC = () => {
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail } = useContext(CRUDPageContext)!;

  const { tableConfig, tableDetail } = useContextTypeSelectors();
  const { getList } = useContextType();

  const onEditClick = useCallback(
    (detail: RowData) => {
      if (detail) {
        setDetail(detail);
        setModalMode(true);
      }
    },
    [setDetail, setModalMode],
  );

  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail: TableColumnDetail) => {
        if (columnDetail.id === "contextType") {
          const TextWithTooltipComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const { contextType } = props?.row?.original || {};

            const contextTypeDetail = props.row.original;

            const onCellClick = (detail: RowData) => {
              onEditClick(detail);
            };

            return (
              <div
                className="is-flex has-ai-c pointer"
                onClick={() => {
                  onCellClick(contextTypeDetail);
                }}
                onKeyDown={noop}
                tabIndex={0}
                role="button"
              >
                <span>{contextType}</span>
              </div>
            );
          };

          columnDetail.cell = TextWithTooltipComponent;
        }

        return columnDetail;
      }) || [],
    [tableConfig?.columns, onEditClick],
  );

  const onLoadMoreClick = () => {
    // const { pageSize, pageOffset } = tableDetail;

    // const payload: GetListParams = {
    //   requireTotal: false,
    //   pageOffset: pageOffset + pageSize,
    //   pageSize,
    // };

    // if (searchTerm) {
    //   payload.name = searchTerm;
    // }

    apiCall(() => getList()).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default Table;
