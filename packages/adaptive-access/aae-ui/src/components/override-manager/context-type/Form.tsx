import { useContext, useEffect, useState } from "react";

import {
  defaultFormPropTypes,
  defaultFormProps,
} from "@zscaler/zui-component-library";

import { noop } from "lodash-es";
import CRUDPageContextProvider, {
  CRUDPageContext,
} from "../../../contexts/CRUDPageContextProvider";
import Actions from "../common/Actions";
import Table from "./detail-container/Table";
import CRUD from "./detail-container/CRUD";
import { useContextType } from "../../../ducks/context-type";
import { useApiCall } from "../../../hooks/useApiCallContext";

const defaultProps = {
  ...defaultFormProps,
};

const Form = () => {
  const { apiCall } = useApiCall();

  const { detail, setDetail, privileges } = useContext(CRUDPageContext)!;

  const [formValues] = useState({
    ...detail,
  });

  const { getInnerList } = useContextType();

  useEffect(() => {
    apiCall(() => getInnerList()).catch(noop);
    setDetail({ ...formValues });
  }, [formValues, setDetail]);

  const renderGeneralSection = () => (
    <>
      <p>
        {detail?.subjectCount?.toString()} Subjects,{" "}
        {detail?.overrideCount?.toString()} Override
      </p>
      <CRUDPageContextProvider privileges={privileges}>
        <Actions />
        <Table />
        <CRUD />
      </CRUDPageContextProvider>
    </>
  );

  return <section className="form-container">{renderGeneralSection()}</section>;
};

Form.defaultProps = defaultProps;

Form.propTypes = { ...defaultFormPropTypes };

export default Form;
