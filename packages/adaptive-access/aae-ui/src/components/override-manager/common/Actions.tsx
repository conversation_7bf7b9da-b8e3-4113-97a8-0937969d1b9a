import type React from "react";
import { useContext, useState } from "react";

import { DropDown } from "@zscaler/zui-component-library";

import { CRUDPageContext } from "../../../contexts/CRUDPageContextProvider";
import { type DropDownOnSelection } from "../../../types/dropdown";

const Actions: React.FC = () => {
  const { privileges } = useContext(CRUDPageContext)!;

  const { hasFullAccess } = privileges;

  const [selectedList, setSelectedList] = useState<DropDownOnSelection[]>([
    { label: "ALL", value: "ALL" },
  ]);

  return (
    <div
      className={`is-flex full-width ${hasFullAccess ? "has-jc-sb" : "has-jc-e"}`}
    >
      {hasFullAccess ? (
        <div className="buttons">
          <DropDown
            list={[
              { label: "ALL", value: "ALL" },
              { label: "OVERRIDEN", value: "OVER<PERSON>DEN" },
            ]}
            selectedList={selectedList}
            onSelection={(detail: DropDownOnSelection[]) => {
              setSelectedList(detail);
            }}
          />
        </div>
      ) : null}
    </div>
  );
};

export default Actions;
