import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalHeader,
  Input,
  mergeFormValues,
  Field,
  Button,
  TableContainer,
  ToggleButton,
} from "@zscaler/zui-component-library";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy } from "@fortawesome/free-solid-svg-icons";
import { isEqual, noop } from "lodash-es";
import { useTranslation } from "react-i18next";
import { getFormValidationDetail } from "../helper";
import { type FormValues } from "../helper";
import { type FormValidationDetail } from "../../../types/modalhelper";
import { type Privileges } from "../../../types/permissions";
import { useIntegrations } from "../../../ducks/integrations";
import { useIntegrationsSelectors } from "../../../ducks/integrations/selectors";
import { useGlobalContext } from "../../../ducks/global";
import { useApiCall } from "../../../hooks/useApiCallContext";

type OktaModalProps = {
  show: boolean;
  onCloseClick: () => void;
  privileges: Privileges;
};

function OktaModal({ show, onCloseClick, privileges }: OktaModalProps) {
  const { apiCall } = useApiCall();
  const {
    getOktaConfigs,
    saveOktaConfigs,
    validateOktaConfigs,
    generatePbks,
    resetConfigs,
  } = useIntegrations(); // Use Context for integrations
  const { showErrorNotification, showSuccessNotification } = useGlobalContext();

  const { configsDetail: oktaConfigs, commonTableConfig: tableConfig } =
    useIntegrationsSelectors(); // Use Context selectors
  const { t } = useTranslation();

  const { hasFullAccess } = privileges;

  const [formValues, setFormValues] = useState<FormValues>({});
  const [validationDetail, setValidationDetail] =
    useState<FormValidationDetail>({});
  const [isSaveClicked, setIsSaveClicked] = useState(false);
  const [isPbksGenerated, setIsPbksGenerated] = useState(false);

  useEffect(() => {
    apiCall(getOktaConfigs).catch(noop);
  }, [getOktaConfigs]);

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...oktaConfigs }));
  }, [oktaConfigs]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({ formValues });
    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onFormFieldChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const formFiledData = mergeFormValues(evt) as FormValues;
    setFormValues(formFiledData);
  };

  const handleCopyToClipboard = () => {
    if (formValues.jwksUrl) {
      navigator.clipboard
        .writeText(formValues.jwksUrl ?? "")
        .then(() => {
          showSuccessNotification({ message: "Copied URL" });
        })
        .catch(() => {
          showErrorNotification({ message: "Failed to copy" });
        });
    }
  };

  useEffect(() => {
    if (formValues?.jwksUrl) {
      setIsPbksGenerated(true);
    }
  }, [formValues]);

  const onStreamStatusButtonsClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      streamStatus: !prevState.streamStatus,
    }));
  };

  const handleGenerateKeys = () => {
    if (formValues.baseUrl && formValues.clientId) {
      apiCall(() =>
        generatePbks({
          baseUrl: formValues.baseUrl,
          clientId: formValues.clientId,
        }),
      )
        .then((res) => {
          if (res?.jwksUrl) {
            showSuccessNotification({
              message: "Keys generated",
            });
            setIsPbksGenerated(true);
            setFormValues((prevState) => ({
              ...prevState,
              jwksUrl: res.jwksUrl,
            }));
          } else {
            showErrorNotification({
              message: "Error in keys generation",
            });
          }
        })
        .catch(() => {
          showErrorNotification({
            message: "Error in keys generation",
          });
        });
    }
  };

  const isGenerateKeysDisabled = () =>
    !(formValues.baseUrl && formValues.clientId);

  const renderBodySection = () => (
    <div>
      <Input
        name="baseUrl"
        label="BASE_URL"
        onChange={onFormFieldChange}
        value={formValues.baseUrl ?? ""}
        disabled={!hasFullAccess}
        maxLength={128}
        info={validationDetail}
      />
      <Input
        name="clientId"
        label="CLIENT_ID"
        onChange={onFormFieldChange}
        value={formValues.clientId ?? ""}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
      />
      <Field label={"PUBLIC_KEYS_URL"}>
        {!isPbksGenerated ? (
          <Button
            type="tertiary"
            onClick={handleGenerateKeys}
            disabled={isGenerateKeysDisabled()}
            containerStyle={{
              paddingLeft: 0,
              width: "50%",
              justifyContent: "left",
            }}
          >
            {t("GENERATE_KEYS")}
          </Button>
        ) : (
          <Button
            containerStyle={{
              paddingLeft: 0,
              width: "50%",
              justifyContent: "left",
            }}
            type="tertiary"
            onClick={handleCopyToClipboard}
          >
            <span className="truncated-text"> {formValues.jwksUrl}</span>
            <FontAwesomeIcon icon={faCopy} className="icon right" />
          </Button>
        )}
      </Field>
      <Field label="STATUS">
        <ToggleButton
          type="success"
          showLabel={false}
          isOn={formValues.streamStatus}
          onToggleClick={onStreamStatusButtonsClick}
        />
      </Field>
    </div>
  );

  const onSaveClick = () => {
    apiCall(() => saveOktaConfigs({ ...formValues }))
      .then((res: string) => {
        showSuccessNotification({
          message: res,
        });
        setIsSaveClicked(true);
      })
      .catch(() => {
        showErrorNotification({
          message: "Error in saving data",
        });
      });
  };

  const onTestIntegrationClick = () => {
    apiCall(validateOktaConfigs)
      .then(() => {
        showSuccessNotification({
          message: "Validation done successfully",
        });
        onCloseClick();
        resetConfigs(); // Reset configs on close
      })
      .catch((err: Error & { error?: string }) => {
        showErrorNotification({
          message: err?.error || err.message || "Test Integration failed",
        });
      });
  };

  useEffect(() => {
    if (!isEqual(formValues, oktaConfigs)) {
      setIsSaveClicked(false);
    } else {
      setIsSaveClicked(true);
    }
  }, [formValues, oktaConfigs]);

  const tableColumnConfig = useMemo(
    () => [...(tableConfig?.columns || [])],
    [tableConfig?.columns],
  );

  const renderCommonTable = () => (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={formValues?.contextSignals ?? []}
      pagination={{
        totalRecord: formValues?.contextSignals?.length,
        hasData: true,
        hasFetchedAllRecords: true,
        hasError: false,
        error: {},
        data: formValues?.contextSignals,
      }}
    />
  );

  const isSaveDisabled = () => !isPbksGenerated || isSaveClicked;

  const renderFooterSection = () => (
    <div style={{ display: "flex", gap: "20px" }}>
      <Button type="primary" onClick={onSaveClick} disabled={isSaveDisabled()}>
        {t("SAVE")}
      </Button>
      {isPbksGenerated && (
        <Button
          type="secondary"
          onClick={onTestIntegrationClick}
          disabled={!isSaveClicked}
        >
          {t("TEST_INTEGRATION")}
        </Button>
      )}
      <Button
        type="tertiary"
        onClick={onCloseClick}
        containerStyle={{ paddingLeft: 0 }}
      >
        {t("CANCEL")}
      </Button>
    </div>
  );

  return (
    <Modal
      align="right"
      isBlocking={false}
      show={show}
      onEscape={onCloseClick}
      containerClass="crud-modal"
    >
      <ModalHeader text="EDIT_OKTA" onClose={onCloseClick} />
      <ModalBody>
        <>
          {renderBodySection()}
          {renderCommonTable()}
        </>
      </ModalBody>
      <ModalFooter>{renderFooterSection()}</ModalFooter>
    </Modal>
  );
}

export default OktaModal;
