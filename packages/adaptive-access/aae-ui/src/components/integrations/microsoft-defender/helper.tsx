import { defaultValidationDetail } from "@zscaler/zui-component-library";

import { FormValidationDetail } from "../../../types/modalhelper";

type FormValues = {
  connectionString?: string;
  streamStatus?: boolean;
  eventHubInstance?: string;
  eventHubHost?: string;
  consumerGroup?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  contextSignals?: any[];
};

export const getFormValidationDetail = ({
  formValues,
}: {
  formValues: FormValues;
}): FormValidationDetail => {
  const validationDetail = { ...defaultValidationDetail };

  const { eventHubInstance, connectionString, eventHubHost, consumerGroup } =
    formValues || {};

  if (!eventHubInstance) {
    validationDetail.isValid = false;
    validationDetail.context = "eventHubInstance";
    validationDetail.type = "error";
    validationDetail.message = "EVENT_HUB_INSTANCE_REQUIRED_MESSAGE";

    return validationDetail;
  }

  if (!connectionString) {
    validationDetail.isValid = false;
    validationDetail.context = "connectionString";
    validationDetail.type = "error";
    validationDetail.message = "CONNECTION_STRING_REQUIRED_MESSAGE";

    return validationDetail;
  }

  if (!eventHubHost) {
    validationDetail.isValid = false;
    validationDetail.context = "eventHubHost";
    validationDetail.type = "error";
    validationDetail.message = "EVENT_HUB_HOST_REQUIRED_MESSAGE";

    return validationDetail;
  }

  if (!consumerGroup) {
    validationDetail.isValid = false;
    validationDetail.context = "connectionString";
    validationDetail.type = "error";
    validationDetail.message = "CONSUMER_GROUP_REQUIRED_MESSAGE";

    return validationDetail;
  }

  return validationDetail;
};
