import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalHeader,
  Input,
  mergeFormValues,
  Field,
  Button,
  TableContainer,
  DropDown,
  ToggleButton,
  PasswordInput,
} from "@zscaler/zui-component-library";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy } from "@fortawesome/pro-regular-svg-icons";
import { isEqual, noop } from "lodash-es";
import { useTranslation } from "react-i18next";
import { getFormValidationDetail } from "../helper";
import { type FormValidationDetail } from "../../../types/modalhelper";
import { type Privileges } from "../../../types/permissions";
import { type DropDownOnSelection } from "../../../types/dropdown";
import { useGlobalContext } from "../../../ducks/global";
import { useIntegrations } from "../../../ducks/integrations";
import { useIntegrationsSelectors } from "../../../ducks/integrations/selectors";
import { useApiCall } from "../../../hooks/useApiCallContext";

type CrowdStrikeModalProps = {
  show: boolean;
  onCloseClick: () => void;
  privileges: Privileges;
};

type FormValues = {
  baseUrl?: string;
  webhookURL?: string;
  customerId?: string;
  clientId?: string;
  clientSecret?: string;
  sharedSecret?: string;
  status?: boolean;
  baseUrls?: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  contextSignals?: any[];
};

export default function CrowdStrikeModal({
  show,
  onCloseClick,
  privileges,
}: CrowdStrikeModalProps) {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();
  const { hasFullAccess } = privileges;

  const {
    getCrowdStrikeConfigs,
    saveCrowdStrikeConfigs,
    validateCrowdStrikeConfigs,
    getBaseUrlAndWebHookUrl,
  } = useIntegrations();
  const { configsDetail: oktaConfigs, commonTableConfig: tableConfig } =
    useIntegrationsSelectors();

  const { showErrorNotification, showSuccessNotification } = useGlobalContext();

  const [formValues, setFormValues] = useState<FormValues>({});
  const [validationDetail, setValidationDetail] =
    useState<FormValidationDetail>({});
  const [isSaveClicked, setIsSaveClicked] = useState(false);

  useEffect(() => {
    // Fetch CrowdStrike configurations and update relevant form values
    apiCall(getCrowdStrikeConfigs).catch(noop);

    apiCall(getBaseUrlAndWebHookUrl)
      .then((res: Record<string, unknown>) => {
        setFormValues((prevState) => ({
          ...prevState,
          webhookURL: res?.webhookUrl as string,
          baseUrls: res?.baseUrls as string[],
        }));
      })
      .catch(noop);
  }, [getCrowdStrikeConfigs, getBaseUrlAndWebHookUrl]);

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...oktaConfigs }));
  }, [oktaConfigs]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
      isCrowdStrike: true,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onFormFieldChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const formFiledData = mergeFormValues(evt) as FormValues;
    setFormValues(formFiledData);
  };

  const handleCopyToClipboard = () => {
    if (formValues.webhookURL) {
      navigator.clipboard
        .writeText(formValues.webhookURL ?? "")
        .then(() => {
          showSuccessNotification({ message: "Copied URL" });
        })
        .catch(() => {
          showErrorNotification({ message: "Failed to copy" });
        });
    }
  };

  const onStreamStatusButtonsClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      status: !prevState.status,
    }));
  };

  const renderBodySection = () => (
    <div>
      <div className="is-flex-row">
        <Field
          label="BASE_URL"
          containerClass="no-m-b"
          containerStyle={{ flex: 1 }}
        >
          <DropDown
            list={
              formValues?.baseUrls?.map((item) => ({
                label: item,
                value: item,
              })) ?? []
            }
            selectedList={[
              { label: formValues?.baseUrl, value: formValues?.baseUrl },
            ]}
            onSelection={(detail: DropDownOnSelection[]) => {
              setFormValues((prevState) => ({
                ...prevState,
                baseUrl: detail[0]?.value,
              }));
            }}
            info={validationDetail}
            containerStyle={{ width: "80%" }}
          />
        </Field>
        <Field label="WEBHOOK_URL" containerStyle={{ flex: 2 }}>
          <Button
            containerStyle={{ paddingLeft: 0 }}
            type="tertiary"
            onClick={handleCopyToClipboard}
          >
            <span className="truncated-text"> {formValues.webhookURL}</span>
            <FontAwesomeIcon icon={faCopy} className="icon right" />
          </Button>
        </Field>
      </div>

      <Input
        name="customerId"
        label="CUSTOMER_ID"
        onChange={onFormFieldChange}
        value={formValues.customerId ?? ""}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
      />

      <Input
        name="clientId"
        label="CLIENT_ID"
        onChange={onFormFieldChange}
        value={formValues.clientId ?? ""}
        maxLength={256}
        disabled={!hasFullAccess}
        info={validationDetail}
      />

      <PasswordInput
        name="clientSecret"
        label="CLIENT_SECRET"
        onChange={onFormFieldChange}
        value={formValues?.clientSecret ?? ""}
        maxLength={256}
        canCopy
        disabled={!hasFullAccess}
        info={validationDetail}
      />

      <PasswordInput
        name="sharedSecret"
        label="SHARED_SECRET"
        onChange={onFormFieldChange}
        value={formValues?.sharedSecret ?? ""}
        maxLength={2048}
        canCopy
        disabled={!hasFullAccess}
        info={validationDetail}
      />
      <Field label="STATUS">
        <ToggleButton
          type="success"
          showLabel={false}
          isOn={formValues.status}
          onToggleClick={onStreamStatusButtonsClick}
        />
      </Field>
    </div>
  );

  const onSaveClick = () => {
    const isUpdate = !!oktaConfigs?.baseUrl;

    apiCall(() => saveCrowdStrikeConfigs({ ...formValues }, isUpdate))
      .then((res) => {
        showSuccessNotification({
          message: res,
        });
        setIsSaveClicked(true);
      })
      .catch(() => {
        showErrorNotification({
          message: "Error in saving data",
        });
      });
  };

  const onTestIntegrationClick = () => {
    apiCall(() => validateCrowdStrikeConfigs({ ...formValues }))
      .then(() => {
        showSuccessNotification({
          message: "Validation done successfully",
        });
        onCloseClick();
      })
      .catch((err: Error) => {
        showErrorNotification({
          message:
            (err as any)?.error || err.message || "Test Integration failed",
        });
      });
  };

  const isPreviousDataEqual = (
    formValues: FormValues,
    oktaConfigs: FormValues,
  ) => {
    const cloneFormValues = { ...formValues };
    delete cloneFormValues.baseUrls;
    delete cloneFormValues.webhookURL;

    return isEqual(cloneFormValues, oktaConfigs);
  };

  useEffect(() => {
    if (!isPreviousDataEqual(formValues, oktaConfigs)) {
      setIsSaveClicked(false);
    } else {
      setIsSaveClicked(true);
    }

    if (
      !formValues.clientId ||
      !formValues.customerId ||
      !formValues.clientSecret
    ) {
      setIsSaveClicked(true);
    }
  }, [formValues, oktaConfigs]);

  const tableColumnConfig = useMemo(
    () => [...(tableConfig?.columns || [])],
    [tableConfig?.columns],
  );

  const renderCommonTable = () => (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={formValues?.contextSignals ?? []}
      pagination={{
        totalRecord: formValues?.contextSignals?.length,
        hasData: true,
        hasFetchedAllRecords: true,
        hasError: false,
        error: {},
        data: formValues?.contextSignals,
      }}
    />
  );

  const isSaveDisabled = () => isSaveClicked || !formValues?.baseUrl;

  const renderFooterSection = () => (
    <div style={{ display: "flex", gap: "20px" }}>
      <Button type="primary" onClick={onSaveClick} disabled={isSaveDisabled()}>
        {t("SAVE")}
      </Button>

      <Button
        type="secondary"
        onClick={onTestIntegrationClick}
        disabled={!isSaveClicked}
      >
        {t("TEST_INTEGRATION")}
      </Button>

      <Button
        type="tertiary"
        onClick={onCloseClick}
        containerStyle={{ paddingLeft: 0 }}
      >
        {t("CANCEL")}
      </Button>
    </div>
  );

  return (
    <Modal
      align="right"
      isBlocking={false}
      show={show}
      onEscape={onCloseClick}
      containerClass="crud-modal"
    >
      <ModalHeader text="EDIT_CROWDSTRIKE" onClose={onCloseClick} />
      <ModalBody>
        <>
          {renderBodySection()}
          {renderCommonTable()}
        </>
      </ModalBody>
      <ModalFooter>{renderFooterSection()}</ModalFooter>
    </Modal>
  );
}
