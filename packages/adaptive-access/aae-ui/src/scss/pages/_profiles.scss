/* DynamicCriteriaBuilder.css */

/* Base Styles */
.dynamic-criteria-builder {
  padding: 1rem 0;
  font-family: sans-serif;
  max-width: 100%;
  overflow-x: auto;
}

.title {
  font-size: 1rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.btn {
  margin-right: 0.5rem;
  border: none;
  background: none;
}

.status {
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

.copy-status {
  color: var(--semantic-color-content-base-primary, #131a2e);
}

.paste-status {
  color: #48bb78;
}

/* Group Header */
.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
  gap: 0.5rem;
}

.group-select {
  padding: 0.35rem 0.5rem;
  border: 1px solid var(--semantic-color-content-base-primary, #131a2e);
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  background-color: var(--semantic-color-background-primary, #fff);
  color: var(--semantic-color-content-base-primary, #131a2e);
  min-width: 80px;
}

.add-button,
.add-subgroup-button,
.delete-group-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border: none;
  background: none;
  cursor: pointer;
}

/* Criteria Items Container */
.criteria-items {
  position: relative;
  padding-left: 2.5rem;
}

/* Connected Lines for Groupings */
.criteria-group {
  position: relative;
}

.criteria-group::before {
  content: "";
  position: absolute;
  top: 2rem;
  left: 1rem;
  z-index: 9;
  bottom: 0.8rem;
  width: 1px;
  background-color: var(--semantic-color-background-primary, #fff);
}

.criteria-item {
  position: relative;
  margin-bottom: 0.5rem;
}

.criteria-item::before {
  content: "";
  position: absolute;
  top: 1rem;
  left: -1.5rem;
  width: 1.5rem;
  height: 1px;
  background-color: var(--semantic-color-background-primary, #fff);
}

.criteria-item:last-child::after {
  content: "";
  position: absolute;
  top: 1rem;
  left: -1.5rem;
  bottom: -0.5rem;
  width: 1px;
  background-color: var(--semantic-color-background-primary, #fff);
}

/* Criterion Row */
.criteria-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  position: relative;
  // flex-wrap: wrap;
}

//   .criteria-row::before {
//     content: "";
//     position: absolute;
//     top: 50%;
//     left: -1.5rem;
//     width: 1.5rem;
//     height: 1px;
//     background-color: #BAC2CF;
//   }

.criteria-select {
  padding: 0.45rem 0.5rem;
  border: 1px solid #bac2cf;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  background-color: var(--semantic-color-background-primary, #fff);
  min-width: 120px;
}

.criteria-input {
  padding: 0.45rem 0.5rem;
  border: 1px solid #bac2cf;
  border-radius: 0.25rem;
  width: 5rem;
  font-size: 0.875rem;
  background-color: var(--semantic-color-background-primary, #fff);
}

.delete-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border: none;
  background: none;
  cursor: pointer;
}

/* Expression Sections */
.expression-section {
  margin-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.expression-title {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
}

.expression {
  background-color: var(--semantic-color-background-primary, #fff);
  padding: 0.75rem;
  border-radius: 0.25rem;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.875rem;
  border: 1px solid #e2e8f0;
  margin-top: 0.375rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .dynamic-criteria-builder {
    padding: 0.5rem;
  }

  .criteria-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .criteria-select,
  .criteria-input {
    width: 100%;
  }
}

/* Flex utilities */
.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

// new group level criterion and sub group
.add-select-container {
  display: inline-block;
  position: relative;
  margin-left: 2.5rem;
  &::before {
    content: "";
    position: absolute;
    top: 1rem;
    left: -1.5rem;
    width: 1.5rem;
    height: 1px;
    background-color: var(--semantic-color-background-primary, #fff);
  }
}

.add-select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
  border: none;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.add-select:hover {
  background-color: rgba(33, 96, 225, 0.1);
}

.add-select:focus {
  outline: none;
}
