import {
  type TableColumnDetail,
  type TableConfig,
  type TableDetail,
} from "../../types/table";

export const REDUCER_KEY = "integrations";

export const OKTA_API_ENDPOINT = "/manager/okta";

export const CROWDSTRIKE_API_ENDPOINT = "/manager/crowdstrike";

export const MICROSOFT_DEFENDER_API_ENDPOINT = "/manager/configs";

export const SILVER_FORT_API_ENDPOINT = "/manager/configs";

export const API_ENDPOINT = "/integrations";

export const DATA_TABLE_DETAIL = "tableDetail";

export const CONFIGS_DETAIL = "oktaConfigs";

export const UPDATE_LIST_ACTION_TYPE = "UPDATE_LIST";
export const UPDATE_CONFIGS_ACTION_TYPE = "UPDATE_CONFIGS";
export const RESET_CONFIGS_ACTION_TYPE = "RESET_CONFIGS";

export type RowData = {
  name?: string;
  source?: string;
  subjectCount?: string;
  overrideCount?: string;
  subjectIdentifier?: string;
  contextValue?: string;
  contextExpiry?: string;
  overrideValue?: string;
  overrideExpiry?: string;
  contextType?: string;
  status?: string;
  streamStatus?: string;
  valueType?: string;
  type?: string;
};

const DEFAULT_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "name",
    accessorFn: (row: RowData) => row.name ?? "-",
    Header: "SOURCE",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: "alphanumeric",
  },
  {
    id: "contextType",
    accessorFn: (row: RowData) => row.contextType ?? "-",
    Header: "CONTEXT_TYPE",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortType: "alphanumeric",
  },
  {
    id: "streamStatus",
    accessorFn: (row: RowData) => row.streamStatus ?? "-",
    Header: "STATUS",
    minSize: 300,
    size: 300,
    defaultCanSort: true,
    sortType: "alphanumeric",
  },
  {
    id: "actions",
    Header: "ACTIONS",
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: "alphanumeric",
  },
];

export const DEFAULT_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL: TableDetail = {
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

const DEFAULT_COMMON_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "name",
    accessorFn: (row: RowData) => row.name ?? "-",
    Header: "SIGNAL",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: "alphanumeric",
  },
  {
    id: "valueType",
    accessorFn: (row: RowData) => row.valueType ?? row.type ?? "-",
    Header: "VALUE_TYPE",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: "alphanumeric",
  },
];

export const DEFAULT_COMMON_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_COMMON_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_CONFIGS_DETAIL: Record<string, unknown> = {};

export type DefaultState = {
  [DATA_TABLE_DETAIL]: TableDetail;
  [CONFIGS_DETAIL]: Record<string, unknown>;
};

export const DEFAULT_STATE: DefaultState = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [CONFIGS_DETAIL]: DEFAULT_CONFIGS_DETAIL,
};
