import { useMemo } from "react";
import { useIntegrations } from "./index";
import {
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_COMMON_TABLE_CONFIG,
  CONFIGS_DETAIL,
  DEFAULT_CONFIGS_DETAIL,
} from "./constants";
import type { TableConfig, TableDetail } from "../../types/table";

export const useIntegrationsSelectors = () => {
  const { state } = useIntegrations();

  // Table Detail
  const tableDetail = useMemo<TableDetail>(
    () => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
    [state],
  );

  // Configurations Detail
  const configsDetail = useMemo<Record<string, unknown>>(
    () => state[CONFIGS_DETAIL] || DEFAULT_CONFIGS_DETAIL,
    [state],
  );

  // Table Configs
  const tableConfig = useMemo<TableConfig>(() => DEFAULT_TABLE_CONFIG, []);
  const commonTableConfig = useMemo<TableConfig>(
    () => DEFAULT_COMMON_TABLE_CONFIG,
    [],
  );

  return {
    tableDetail,
    configsDetail,
    tableConfig,
    commonTableConfig,
  };
};
