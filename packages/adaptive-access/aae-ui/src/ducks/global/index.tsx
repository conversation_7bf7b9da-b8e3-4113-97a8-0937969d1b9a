// GlobalContext.tsx
"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useRef,
  useMemo,
  type ReactNode,
} from "react";
import {
  APP_STATE,
  DATA_APP_STATE,
  DATA_APP_SETUP_DETAIL,
  DATA_LOADING,
  DATA_NOTIFICATIONS,
  DATA_BUILD_VERSION,
  DEFAULT_APP_SETUP_DETAIL,
  DEFAULT_STATE,
  NOTIFICATIONS_TYPE,
} from "./constants";

// Types
export type Notification = {
  notificationId?: number;
  type?: string;
  message?: string;
  timestamp?: number;
  autoHide?: boolean;
};

type GlobalState = {
  [DATA_APP_STATE]: (typeof APP_STATE)[keyof typeof APP_STATE];
  [DATA_APP_SETUP_DETAIL]: object;
  [DATA_LOADING]: boolean;
  [DATA_NOTIFICATIONS]: Record<number, Notification>;
  [DATA_BUILD_VERSION]: {
    buildTime: string;
    buildVersion: string;
  };
};

type GlobalAction =
  | {
      type: "UPDATE_APP_STATE";
      payload: (typeof APP_STATE)[keyof typeof APP_STATE];
    }
  | { type: "UPDATE_APP_SETUP_DETAIL"; payload?: object }
  | { type: "UPDATE_LOADING"; payload: boolean }
  | { type: "ADD_NOTIFICATION"; payload: Notification }
  | { type: "REMOVE_NOTIFICATION"; payload: number };

type GlobalContextType = {
  state: GlobalState;
  appSetupReset: () => void;
  appSetupDone: () => void;
  appSetupError: (errorDetail: object) => void;
  showLoader: () => void;
  hideLoader: () => void;
  showNotification: (notificationDetail?: Notification) => number;
  hideNotification: (id: number) => void;
  showSuccessNotification: (notificationDetail: Notification) => number;
  showWarningNotification: (notificationDetail?: Notification) => number;
  showErrorNotification: (notificationDetail: Notification) => number;
};

// Context
const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

// Reducer
function globalReducer(state: GlobalState, action: GlobalAction): GlobalState {
  switch (action.type) {
    case "UPDATE_APP_STATE":
      return {
        ...state,
        [DATA_APP_STATE]: action.payload,
      };
    case "UPDATE_APP_SETUP_DETAIL":
      return {
        ...state,
        [DATA_APP_SETUP_DETAIL]: action.payload ?? DEFAULT_APP_SETUP_DETAIL,
      };
    case "UPDATE_LOADING":
      return {
        ...state,
        [DATA_LOADING]: action.payload,
      };
    case "ADD_NOTIFICATION": {
      const { payload } = action;
      const notificationId = payload.notificationId as number;

      return {
        ...state,
        [DATA_NOTIFICATIONS]: {
          ...state[DATA_NOTIFICATIONS],
          [notificationId]: {
            ...payload,
            notificationId,
          },
        },
      };
    }
    case "REMOVE_NOTIFICATION": {
      const newNotifications = { ...state[DATA_NOTIFICATIONS] };
      delete newNotifications[action.payload];

      return {
        ...state,
        [DATA_NOTIFICATIONS]: newNotifications,
      };
    }
    default:
      return state;
  }
}

// Provider
export const GlobalProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(
    globalReducer,
    DEFAULT_STATE as GlobalState,
  );
  const notificationCounterRef = useRef(0);

  // Action creators
  const appSetupReset = useCallback(() => {
    dispatch({ type: "UPDATE_APP_STATE", payload: APP_STATE.UNSET });
    dispatch({ type: "UPDATE_APP_SETUP_DETAIL", payload: {} });
  }, []);

  const appSetupDone = useCallback(() => {
    dispatch({ type: "UPDATE_APP_STATE", payload: APP_STATE.SETUP_DONE });
    dispatch({ type: "UPDATE_APP_SETUP_DETAIL", payload: {} });
  }, []);

  const appSetupError = useCallback((errorDetail: object) => {
    dispatch({ type: "UPDATE_APP_STATE", payload: APP_STATE.SETUP_ERROR });
    dispatch({ type: "UPDATE_APP_SETUP_DETAIL", payload: errorDetail });
  }, []);

  const showLoader = useCallback(() => {
    dispatch({ type: "UPDATE_LOADING", payload: true });
  }, []);

  const hideLoader = useCallback(() => {
    dispatch({ type: "UPDATE_LOADING", payload: false });
  }, []);

  const showNotification = useCallback(
    (notificationDetail: Notification = {}) => {
      const notificationId =
        notificationDetail.notificationId ?? ++notificationCounterRef.current;
      dispatch({
        type: "ADD_NOTIFICATION",
        payload: {
          ...notificationDetail,
          notificationId,
          timestamp: Date.now(),
        },
      });
      return notificationId;
    },
    [],
  );

  const hideNotification = useCallback((id: number) => {
    dispatch({ type: "REMOVE_NOTIFICATION", payload: id });
  }, []);

  const showSuccessNotification = useCallback(
    (notificationDetail: Notification) => {
      const notificationId =
        notificationDetail.notificationId ?? ++notificationCounterRef.current;
      dispatch({
        type: "ADD_NOTIFICATION",
        payload: {
          ...notificationDetail,
          type: NOTIFICATIONS_TYPE.SUCCESS,
          notificationId,
          timestamp: Date.now(),
        },
      });
      return notificationId;
    },
    [],
  );

  const showWarningNotification = useCallback(
    (notificationDetail: Notification = {}) => {
      const notificationId =
        notificationDetail.notificationId ?? ++notificationCounterRef.current;
      dispatch({
        type: "ADD_NOTIFICATION",
        payload: {
          ...notificationDetail,
          type: notificationDetail.type ?? NOTIFICATIONS_TYPE.WARNING,
          notificationId,
          timestamp: Date.now(),
        },
      });
      return notificationId;
    },
    [],
  );

  const showErrorNotification = useCallback(
    (notificationDetail: Notification) => {
      const notificationId =
        notificationDetail.notificationId ?? ++notificationCounterRef.current;
      dispatch({
        type: "ADD_NOTIFICATION",
        payload: {
          ...notificationDetail,
          type: NOTIFICATIONS_TYPE.ERROR,
          autoHide: notificationDetail.autoHide ?? false,
          notificationId,
          timestamp: Date.now(),
        },
      });
      return notificationId;
    },
    [],
  );

  const contextValue = useMemo(
    () => ({
      state,
      appSetupReset,
      appSetupDone,
      appSetupError,
      showLoader,
      hideLoader,
      showNotification,
      hideNotification,
      showSuccessNotification,
      showWarningNotification,
      showErrorNotification,
    }),
    [
      state,
      appSetupReset,
      appSetupDone,
      appSetupError,
      showLoader,
      hideLoader,
      showNotification,
      hideNotification,
      showSuccessNotification,
      showWarningNotification,
      showErrorNotification,
    ],
  );

  return (
    <GlobalContext.Provider value={contextValue}>
      {children}
    </GlobalContext.Provider>
  );
};

// Custom hook
export const useGlobalContext = (): GlobalContextType => {
  const context = useContext(GlobalContext);
  if (!context) {
    throw new Error("useGlobalContext must be used within a GlobalProvider");
  }
  return context;
};
