// useGlobalSelectors.tsx
import { useMemo } from "react";
import { useGlobalContext, type Notification } from "./index";
import {
  DATA_APP_STATE,
  DATA_APP_SETUP_DETAIL,
  DATA_LOADING,
  DATA_NOTIFICATIONS,
  DATA_BUILD_VERSION,
  DEFAULT_APP_SETUP_DETAIL,
  DEFAULT_APP_STATE,
  DEFAULT_BUILD_VERSION,
  DEFAULT_LOADING,
  DEFAULT_NOTIFICATIONS,
} from "./constants";

export const useGlobalSelectors = () => {
  const { state } = useGlobalContext();

  // App State selectors
  const appState = useMemo(
    () => state[DATA_APP_STATE] || DEFAULT_APP_STATE,
    [state],
  );

  const isAppSetupPending = useMemo(() => appState === "NOT_SET", [appState]);

  const isAppSetupDone = useMemo(() => appState === "SETUP_DONE", [appState]);

  const hasAppSetupError = useMemo(
    () => appState === "SETUP_ERROR",
    [appState],
  );

  // App setup detail selector
  const appSetupDetail = useMemo(
    () => state[DATA_APP_SETUP_DETAIL] || DEFAULT_APP_SETUP_DETAIL,
    [state],
  );

  // Loading state selector
  const isLoading = useMemo(
    () => state[DATA_LOADING] || DEFAULT_LOADING,
    [state],
  );

  // Notifications selector
  const notificationsList = useMemo(
    () =>
      Object.values(
        state[DATA_NOTIFICATIONS] || DEFAULT_NOTIFICATIONS,
      ) as Notification[],
    [state],
  );

  // Build version selector
  const buildVersion = useMemo(
    () => state[DATA_BUILD_VERSION] || DEFAULT_BUILD_VERSION,
    [state],
  );

  return {
    appState,
    isAppSetupPending,
    isAppSetupDone,
    hasAppSetupError,
    appSetupDetail,
    isLoading,
    notificationsList,
    buildVersion,
  };
};
