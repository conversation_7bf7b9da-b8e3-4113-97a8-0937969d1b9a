// SetupInitializer.tsx
"use client";

import { useEffect, type ReactNode } from "react";
import { useSetupFunctions } from "./setupWithContext";

type SetupInitializerProps = {
  children: ReactNode;
  rootId?: string;
};

export const SetupInitializer: React.FC<SetupInitializerProps> = ({
  children,
  rootId,
}) => {
  const { setupUseApiCallFunctionsRegistry, setupFloatingPortalRootId } =
    useSetupFunctions();

  // Setup functions that need access to context
  useEffect(() => {
    setupUseApiCallFunctionsRegistry();
    setupFloatingPortalRootId({ rootId });
  }, [setupUseApiCallFunctionsRegistry, setupFloatingPortalRootId, rootId]);

  return <>{children}</>;
};
