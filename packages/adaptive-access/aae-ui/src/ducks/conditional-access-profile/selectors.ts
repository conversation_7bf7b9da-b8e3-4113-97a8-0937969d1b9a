import { useMemo } from "react";
import { useProfileContext } from "./index";
import {
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  DEFAULT_DYNAMIC_FIELDS_DETAIL,
  DATA_TABLE_DETAIL,
  DATA_DYNAMIC_FIELDS_DETAIL,
} from "./constants";
import type { DynamicField } from "../../components/conditional-access-profile/types/query-builder";
import { TableConfig, TableDetail } from "../../types/table";

export const useProfileSelectors = () => {
  const { state } = useProfileContext();

  // Table selectors
  const tableDetail = useMemo<TableDetail>(
    () => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
    [state],
  );

  const tableConfig = useMemo<TableConfig>(() => DEFAULT_TABLE_CONFIG, []);

  // Dynamic Fields selectors
  const dynamicFields = useMemo<DynamicField[]>(
    () => state[DATA_DYNAMIC_FIELDS_DETAIL] || DEFAULT_DYNAMIC_FIELDS_DETAIL,
    [state],
  );

  return {
    tableDetail,
    tableConfig,
    dynamicFields,
  };
};
