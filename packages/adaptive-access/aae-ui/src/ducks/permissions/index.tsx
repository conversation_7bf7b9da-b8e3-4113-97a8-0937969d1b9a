"use client";

import type React from "react";
import {
  createContext,
  useContext,
  useReducer,
  use<PERSON>allback,
  type ReactNode,
} from "react";
import {
  DATA_ALL_PERMISSIONS,
  DATA_ALL_PERMISSION_LEVELS,
  DATA_MY_PERMISSIONS,
  DATA_MY_PERMISSIONS_LEVEL,
  DEFAULT_ALL_PERMISSIONS,
  DEFAULT_ALL_PERMISSION_LEVELS,
  DEFAULT_MY_PERMISSIONS,
  DEFAULT_MY_PERMISSIONS_LEVEL,
  DEFAULT_STATE,
  NONE_PERMISSION_BLACK_LIST,
} from "./constants";
import { getHasPermissionDetail } from "./helper";
import {
  Permission,
  PermissionLevel,
  PermissionsKey,
  PermissionsState,
  Privileges,
} from "../../types/permissions";
import { PERMISSION_LEVEL } from "../../config";

// Types
type PermissionsAction =
  | { type: "UPDATE_ALL_PERMISSIONS"; payload: Permission[] }
  | { type: "UPDATE_MY_PERMISSIONS"; payload: string[] };

type PermissionsContextType = {
  state: PermissionsState;
  getAllPermissions: () => Promise<void>;
  getMyPermissions: () => Promise<void>;
  getPermissionsByKey: (key: string) => Privileges;
};

// Context
const PermissionsContext = createContext<PermissionsContextType | undefined>(
  undefined,
);

// Reducer
function permissionsReducer(
  state: PermissionsState,
  action: PermissionsAction,
): PermissionsState {
  switch (action.type) {
    case "UPDATE_ALL_PERMISSIONS": {
      const newPermissionLevels: { [key in PermissionsKey]?: string[] } = {};

      action.payload?.forEach(({ name }) => {
        const [permissionKey, level] = name?.split?.(".") || [];
        const typedPermissionKey = permissionKey as PermissionsKey;

        let levelList = newPermissionLevels[typedPermissionKey];

        if (!levelList) {
          levelList = [];

          if (!NONE_PERMISSION_BLACK_LIST.includes(typedPermissionKey)) {
            levelList.push(PERMISSION_LEVEL.NONE);
          }
        }

        levelList.push(level);
        newPermissionLevels[typedPermissionKey] = levelList;
      });

      return {
        ...state,
        [DATA_ALL_PERMISSION_LEVELS]:
          newPermissionLevels || DEFAULT_ALL_PERMISSION_LEVELS,
        [DATA_ALL_PERMISSIONS]: action.payload || DEFAULT_ALL_PERMISSIONS,
      };
    }
    case "UPDATE_MY_PERMISSIONS": {
      const newPermissions: Record<string, Privileges> = {};

      action.payload?.forEach((permission) => {
        const [permissionKey, level] = permission?.split?.(".") || [];
        const typedLevelKey = level as PermissionLevel;
        const hasPermissionDetail = getHasPermissionDetail(typedLevelKey);

        newPermissions[permissionKey] = hasPermissionDetail;
      });

      return {
        ...state,
        [DATA_MY_PERMISSIONS_LEVEL]:
          newPermissions || DEFAULT_MY_PERMISSIONS_LEVEL,
        [DATA_MY_PERMISSIONS]: action.payload || DEFAULT_MY_PERMISSIONS,
      };
    }
    default:
      return state;
  }
}

// Provider
export const PermissionsProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(permissionsReducer, DEFAULT_STATE);

  const getAllPermissions = useCallback(async () => {
    const mockData: Permission[] = [
      {
        name: "sign-on-policy.full",
        description: "Full Access on Sign On Policy",
      },
      {
        name: "sign-on-policy.view",
        description: "View Only Access on Sign On Policy",
      },
      {
        name: "authentication-methods.full",
        description: "Full Access on Authentication Methods",
      },
      {
        name: "authentication-methods.view",
        description: "View Only Access on Authentication Methods",
      },
      {
        name: "user-and-group.full",
        description: " Full Access on Users And Groups",
      },
      {
        name: "user-and-group.view",
        description: "View Only Access on Users And Groups",
      },
      {
        name: "user-credential.full",
        description: "Full Access on User Credentials",
      },
      {
        name: "user-credential.view",
        description: "View Only Access on User Credentials",
      },
      {
        name: "external-identity.full",
        description: "Full Access on External Identities",
      },
      {
        name: "external-identity.view",
        description: "View Only Access on External Identities",
      },
      {
        name: "external-identity.restrictedview",
        description: "Restricted View Access on External Identities",
      },
      {
        name: "ip-location.full",
        description: "Full Access on Ip Locations",
      },
      {
        name: "ip-location.view",
        description: "View Only Access on IpLocations",
      },
      {
        name: "linked-tenant.view",
        description: "View Only Access on Linked Tenants",
      },
      {
        name: "authentication-session.full",
        description: "Full Access on Authentication Session",
      },
      {
        name: "authentication-session.view",
        description: "View Only Access on Authentication Session",
      },
      {
        name: "administrative-entitlement.full",
        description: "Full Access on Administrative Entitlements",
      },
      {
        name: "administrative-entitlement.restrictedfull",
        description: "Restricted Full Access on Administrative Entitlements",
      },
      {
        name: "administrative-entitlement.view",
        description: "View Only Access on Administrative Entitlements",
      },
      {
        name: "service-entitlement.full",
        description: "Full Access on Service Entitlements",
      },
      {
        name: "service-entitlement.view",
        description: "View Only Access on Service Entitlements",
      },
      {
        name: "audit-Log.view",
        description: "View Only Access on Audit Logs",
      },
      {
        name: "authentication-event-log.view",
        description: "View Only Access on Authentication Event Log",
      },
      {
        name: "role.full",
        description: "Full Access on Roles",
      },
      {
        name: "role.view",
        description: "View Only Access on Roles",
      },
      {
        name: "guest-domain.full",
        description: "Full Access on Guest Domain",
      },
      {
        name: "guest-domain.view",
        description: "View Only on Guest Domain",
      },
      {
        name: "remote-assistance-management.full",
        description: "Full Access on Remote Assistance Management",
      },
      {
        name: "remote-assistance-management.view",
        description: "View Only on Remote Assistance Management",
      },
      {
        name: "branding.full",
        description: "Full Access on Branding",
      },
      {
        name: "branding.view",
        description: "View Only on Branding",
      },
      {
        name: "api-clients-and-resources.full",
        description: "Full Access on API clients and resources management",
      },
      {
        name: "api-clients-and-resources.view",
        description: "View Only on API clients and resources management",
      },
      {
        name: "cxo-insight.full",
        description: "Full Access on CXO Insight App",
      },
      {
        name: "cxo-insight.none",
        description: "None on CXO Insight App",
      },
    ];

    dispatch({ type: "UPDATE_ALL_PERMISSIONS", payload: mockData || [] });
    return Promise.resolve();
  }, []);

  const getMyPermissions = useCallback(async () => {
    const mockData: string[] = [
      "ip-location.full",
      "linked-tenant.view",
      "external-identity.full",
      "guest-domain.full",
      "role.full",
      "sign-on-policy.full",
      "authentication-session.full",
      "administrative-entitlement.full",
      "authentication-methods.full",
      "user-and-group.full",
      "service-entitlement.full",
      "user-credential.full",
      "branding.full",
      "authentication-event-log.view",
      "audit-Log.view",
      "remote-assistance-management.full",
    ];

    dispatch({ type: "UPDATE_MY_PERMISSIONS", payload: mockData });
    return Promise.resolve();
  }, []);

  const getPermissionsByKey = useCallback(
    (key: string): Privileges => {
      const myPermissionsLevel =
        state[DATA_MY_PERMISSIONS_LEVEL] || DEFAULT_MY_PERMISSIONS_LEVEL;
      return (
        myPermissionsLevel[key] || {
          hasFullAccess: false,
          hasRestrictedFullAccess: false,
          hasViewAccess: false,
          hasRestrictedViewAccess: false,
          noAccess: true,
        }
      );
    },
    [state],
  );

  return (
    <PermissionsContext.Provider
      value={{
        state,
        getAllPermissions,
        getMyPermissions,
        getPermissionsByKey,
      }}
    >
      {children}
    </PermissionsContext.Provider>
  );
};

// Hook
export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error("usePermissions must be used within a PermissionsProvider");
  }
  return context;
};
