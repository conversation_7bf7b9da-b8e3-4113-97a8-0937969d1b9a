import { PERMISSION_LEVEL } from "../../config";
import { type PermissionLevel, type Privileges } from "../../types/permissions";

// Define the function signature with `level` typed as `PERMISSION_LEVEL`
export const getHasPermissionDetail = (level: PermissionLevel): Privileges => {
  const detail: Privileges = {
    hasFullAccess: false,
    hasRestrictedFullAccess: false,
    hasViewAccess: false,
    hasRestrictedViewAccess: false,
    noAccess: true,
  };

  switch (level) {
    case PERMISSION_LEVEL.FULL: {
      detail.hasFullAccess = true;
      detail.hasRestrictedFullAccess = true;
      detail.hasViewAccess = true;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;
      break;
    }

    case PERMISSION_LEVEL.RESTRICTED_FULL: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = true;
      detail.hasViewAccess = true;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;
      break;
    }

    case PERMISSION_LEVEL.VIEW: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = false;
      detail.hasViewAccess = true;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;
      break;
    }

    case PERMISSION_LEVEL.RESTRICTED_VIEW: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = false;
      detail.hasViewAccess = false;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;
      break;
    }

    case PERMISSION_LEVEL.NONE: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = false;
      detail.hasViewAccess = false;
      detail.hasRestrictedViewAccess = false;
      detail.noAccess = true;
      break;
    }
  }

  return detail;
};
