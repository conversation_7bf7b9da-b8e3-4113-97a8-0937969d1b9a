import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

import "./styles.css";

export { default as AppProvider } from "./zuxp-layout/AppProvider";
export { default as ErrorBoundaryWrapper } from "./components/error-boundary/ErrorBoundaryWrapper";
export { default as SignalHistoryPage } from "./pages/SignalHistoryPage";
export { default as ProfilesPage } from "./pages/ProfilesPage";
export { default as IntegrationsPage } from "./pages/IntegrationsPage";
export { default as OverrideManagerPage } from "./pages/OverrideManagerPage";
export { initializeHttpClient } from "./utils/http/http";
export { default as http } from "./utils/http/http";
export { ResponsiveContainer } from "./components/common/ResponsiveContainer";
export { SignalHistoryProvider } from "./ducks/signal-history";
export { ProfileProvider } from "./ducks/conditional-access-profile";
export { IntegrationsProvider } from "./ducks/integrations";
export { SubjectIdentifierProvider } from "./ducks/subject-identifier";
export { ContextTypeProvider } from "./ducks/context-type";
export { type FeatureFlagsType } from "./zuxp-layout/FeatureFlagsProvider";

// Page Wrappers
export { default as IntegrationsPageWrapper } from "./wrappers/IntegrationsPageWrapper";
export { default as ProfilesPageWrapper } from "./wrappers/ProfilesPageWrapper";
export { default as OverrideManagerPageWrapper } from "./wrappers/OverrideManagerPageWrapper";
export { default as SignalHistoryPageWrapper } from "./wrappers/SignalHistoryPageWrapper";

// Using cn vs classNames will merge dynamic classes and run them through tailwind-merge
// Concept borrowed from shadcn/ui
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}
