import React, { createContext, useContext, ReactNode } from "react";

export type FeatureFlagsType = {
  isAAEIntegrationEnabled: boolean;
  // Add additional features as needed
};

// Define the shape of the feature flags context value
type FeatureFlagsContextType = {
  featureFlags: FeatureFlagsType;
};

// Create a context with the proper type
const FeatureFlagsContext = createContext<FeatureFlagsContextType | undefined>(
  undefined,
);

type FeatureFlagsProviderProps = {
  children: ReactNode;
  featureFlags: FeatureFlagsType;
};

export const FeatureFlagsProvider = ({
  children,
  featureFlags,
}: FeatureFlagsProviderProps) => {
  return (
    <FeatureFlagsContext.Provider value={{ featureFlags }}>
      {children}
    </FeatureFlagsContext.Provider>
  );
};

export const useFeatureFlags = (): FeatureFlagsContextType => {
  const context = useContext(FeatureFlagsContext);

  if (!context) {
    throw new Error(
      "useFeatureFlags must be used within a FeatureFlagsProvider",
    );
  }

  return context;
};
