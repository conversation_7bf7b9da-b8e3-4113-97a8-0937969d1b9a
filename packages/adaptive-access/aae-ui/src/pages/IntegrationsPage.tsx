import type React from "react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { noop } from "lodash-es";

import NoAccess from "../components/no-access/NoAccess";
import Table from "../components/integrations/Table";

import { PERMISSIONS_KEY } from "../config";
import { usePermissions } from "../ducks/permissions";
import { useIntegrations } from "../ducks/integrations";
import { useApiCall } from "../hooks/useApiCallContext";

const IntegrationsPage: React.FC = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const { getPermissionsByKey, getAllPermissions } = usePermissions();
  const { getList } = useIntegrations();
  const privileges = getPermissionsByKey(PERMISSIONS_KEY.ROLES_POLICY);

  // const [searchTerm, setSearchTerm] = useState("");

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(() => getList()).catch(noop);
    apiCall(() => getAllPermissions()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  // will be used later
  // const onSearchEnter = (term) => {
  //   apiCall(getList({ name: term })).catch(noop);
  //   setSearchTerm(term);
  // };

  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "0.5rem",
        }}
      >
        <section className="heading-small page-title">
          {t("INTEGRATIONS")}
        </section>
        {/* <Search
            onSearch={onSearchEnter}
            term={searchTerm}
            containerClass="no-m-r"
            containerStyle={{ maxWidth: "258px", marginBottom: "0px" }}
          /> */}
      </div>
      <Table privileges={privileges} />
    </>
  );
};

export default IntegrationsPage;
