import { useState } from "react";
import type React from "react";
import { useTranslation } from "react-i18next";

import { Tab, Tabs, Search } from "@zscaler/zui-component-library";

import { noop } from "lodash-es";
import NoAccess from "../components/no-access/NoAccess";

import { PERMISSIONS_KEY } from "../config";
import SubjectIdentifierContainer from "../components/override-manager/subject-identifier/SubjectIdentifierContainer";
import ContextTypeContainer from "../components/override-manager/context-type/ContextTypeContainer";
import { usePermissions } from "../ducks/permissions";
import { useSubjectIdentifierContext } from "../ducks/subject-identifier";
import { useApiCall } from "../hooks/useApiCallContext";

const TABS = {
  SUBJECT_IDENTIFIER: "SUBJECT_IDENTIFIER",
  CONTEXT_TYPE: "CONTEXT_TYPE",
};

const OverrideManagerPage: React.FC = () => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall();
  const { getPermissionsByKey } = usePermissions();
  const { getList } = useSubjectIdentifierContext();
  const privileges = getPermissionsByKey(PERMISSIONS_KEY.ROLES_POLICY);

  const { noAccess } = privileges;

  const [selectedTab, setSelectedTab] = useState(TABS.SUBJECT_IDENTIFIER);
  const [searchTerm, setSearchTerm] = useState("");

  if (noAccess) {
    return <NoAccess />;
  }

  const onSearchEnter = (term: string) => {
    if (selectedTab === TABS.SUBJECT_IDENTIFIER) {
      apiCall(() => getList({ name: term })).catch(noop);
    } else {
      // need to call context type api later
    }

    setSearchTerm(term);
  };

  const renderAttributesTabsSelectionSection = () => (
    <>
      {/* <span style={{ display: "block", marginBottom: "4px" }}>
        {t("VIEW_BY")}
      </span> */}
      <div className="flex justify-between">
        <Tabs>
          <Tab
            label="SUBJECT_IDENTIFIER"
            isActive={selectedTab === TABS.SUBJECT_IDENTIFIER}
            onClick={() => {
              setSelectedTab(TABS.SUBJECT_IDENTIFIER);
            }}
          />

          {/* <Tab
            label="CONTEXT_TYPE"
            isActive={selectedTab === TABS.CONTEXT_TYPE}
            onClick={() => {
              setSelectedTab(TABS.CONTEXT_TYPE);
            }}
          /> */}
        </Tabs>
        <Search
          onSearch={onSearchEnter}
          term={searchTerm}
          containerClass="no-m-r"
          containerStyle={{ maxWidth: "258px" }}
        />
      </div>
    </>
  );

  return (
    <>
      <section className="heading-small page-title">{t("OVERRIDES")}</section>

      {renderAttributesTabsSelectionSection()}

      {selectedTab === TABS.SUBJECT_IDENTIFIER && (
        <SubjectIdentifierContainer privileges={privileges} />
      )}
      {selectedTab === TABS.CONTEXT_TYPE && (
        <ContextTypeContainer privileges={privileges} />
      )}
    </>
  );
};

export default OverrideManagerPage;
