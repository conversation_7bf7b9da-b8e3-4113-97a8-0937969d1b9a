import { type PERMISSION_LEVEL, type PERMISSIONS_KEY } from "../config";
import {
  type DATA_ADMIN_ENTITLEMENT_PERMISSIONS,
  type DATA_ALL_PERMISSION_LEVELS,
  type DATA_ALL_PERMISSIONS,
  type DATA_MY_PERMISSIONS,
  type DATA_MY_PERMISSIONS_LEVEL,
} from "../ducks/permissions/constants";

export type PermissionsKey =
  (typeof PERMISSIONS_KEY)[keyof typeof PERMISSIONS_KEY];

export type PermissionLevel =
  (typeof PERMISSION_LEVEL)[keyof typeof PERMISSION_LEVEL];

export type Permission = {
  name: string;
  description: string;
};

export type Privileges = {
  hasFullAccess?: boolean;
  hasRestrictedFullAccess?: boolean;
  hasViewAccess?: boolean;
  hasRestrictedViewAccess?: boolean;
  noAccess?: boolean;
};

export type PermissionsState = {
  [DATA_ALL_PERMISSIONS]: Permission[];
  [DATA_ALL_PERMISSION_LEVELS]: Record<string, string[]>;
  [DATA_MY_PERMISSIONS]: string[];
  [DATA_MY_PERMISSIONS_LEVEL]: Record<string, Privileges>;
  [DATA_ADMIN_ENTITLEMENT_PERMISSIONS]: string[];
};
