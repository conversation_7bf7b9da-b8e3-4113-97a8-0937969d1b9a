import { defineConfig } from "tsup";
import copy from "esbuild-plugin-copy";

export default defineConfig({
  entry: ["src/index.ts", "!src/**/*.test.*"],
  format: ["esm"],
  splitting: true,
  sourcemap: false,
  minify: true,
  clean: true,
  silent: true,
  dts: {
    resolve: true,
  },
  external: [
    "react",
    "react-dom",
    "next",
    "@zscaler/zui-component-library",
    "i18next",
    "react-i18next",
    "axios",
    "lodash-es",
    "dayjs"
  ],
  treeshake: true,
  esbuildPlugins: [
    // Copy SCSS file(s) into "dist"
    copy({
      // Specify the files or directories to copy
      assets: [
        // Copy .scss files in zuxp-layout
        {
          from: "./src/zuxp-layout/**/*.scss",
          to: "./",
        },
        // Copy ALL SCSS from the "scss" folder
        {
          from: "./src/scss/**/*", // Copy entire SCSS folder contents
          to: "./scss", // Maintain relative location (e.g., dist/scss/main.scss)
        },
      ],
    }),
  ],
});
