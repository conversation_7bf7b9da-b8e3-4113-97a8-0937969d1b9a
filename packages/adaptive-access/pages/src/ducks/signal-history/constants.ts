import { getTimeRange } from "@zscaler/zui-component-library";
import dayjs from "dayjs";
import {
  type GetTimeRangeFunction,
  type TableColumnDetail,
  type TableConfig,
  type TableDetail,
} from "../../types/table";
import { type FilterOption } from "../../types/dropdown";

export const REDUCER_KEY = "signal-history";

export const API_ENDPOINT = "/search/events";

export const DATA_TABLE_DETAIL = "tableDetail";

type RowData = {
  subject?: string;
  subject_type?: string;
  source_name?: string;
  context_type?: string;
  impact_score?: string;
  timestamp?: string;
  time?: string;
  end_time?: string;
  valueType?: string;
};

const DEFAULT_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "subject",
    accessorFn: (row: RowData) => row.subject ?? "-",
    Header: "SUBJECT",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "subject_type",
    accessorFn: (row: RowData) => row.subject_type ?? "-",
    Header: "SUBJECT_TYPE",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "source_name",
    accessorFn: (row: RowData) => row.source_name ?? "-",
    Header: "SOURCE",
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "context_type",
    accessorFn: (row: RowData) => row.context_type ?? "-",
    Header: "CONTEXT_TYPE",
    minSize: 250,
    size: 250,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "impact_score",
    accessorFn: (row: RowData) =>
      row.impact_score && row.impact_score !== "null" ? row.impact_score : "-",
    Header: "VALUE",
    minSize: 150,
    size: 150,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "timestamp",
    accessorFn: (row: RowData) =>
      (row.time ?? row.timestamp)
        ? dayjs(row.time ?? row.timestamp).format("MMMM DD, YYYY - hh:mm A")
        : "-",
    Header: "CREATION",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "end_time",
    accessorFn: (row: RowData) =>
      row.end_time
        ? dayjs(row.end_time).format("MMMM DD, YYYY - hh:mm A")
        : "-",
    Header: "EXPIRY",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "valueType",
    accessorFn: (row: RowData) => row.valueType ?? "-",
    Header: "VALUE_TYPE",
    minSize: 150,
    size: 150,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
];

export const DEFAULT_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "number" }],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL: TableDetail = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DATA_TIME_RANGE_OPTIONS = "timeRangeOptions";

const typedGetTimeRange = getTimeRange as GetTimeRangeFunction;

export const DEFAULT_TIME_RANGE_OPTION_DETAIL: FilterOption[] =
  typedGetTimeRange("REPORT").map((timeRange) => ({
    label: timeRange,
    value: timeRange,
  }));

export const DATA_SOURCES = "sources";

export const DEFAULT_SOURCES: unknown[] = [];

export const DATA_CONTEXT_TYPES = "contextTypes";

export const DEFAULT_CONTEXT_TYPES: unknown[] = [];

export const DATA_SUBJECTS = "subjects";

export const DEFAULT_SUBJECTS: unknown[] = [];

export type DefaultState = {
  [DATA_TABLE_DETAIL]: TableDetail;
  [DATA_SOURCES]: unknown[];
  [DATA_CONTEXT_TYPES]: unknown[];
  [DATA_TIME_RANGE_OPTIONS]: FilterOption[];
  [DATA_SUBJECTS]: unknown[];
};

export const DEFAULT_STATE: DefaultState = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_SOURCES]: DEFAULT_SOURCES,
  [DATA_CONTEXT_TYPES]: DEFAULT_CONTEXT_TYPES,
  [DATA_TIME_RANGE_OPTIONS]: DEFAULT_TIME_RANGE_OPTION_DETAIL,
  [DATA_SUBJECTS]: DEFAULT_SUBJECTS,
};
