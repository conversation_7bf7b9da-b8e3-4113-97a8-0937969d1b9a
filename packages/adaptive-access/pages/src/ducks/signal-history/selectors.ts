import { useMemo } from "react";
import { getDropDownList } from "@zscaler/zui-component-library";
import { useSignalHistory } from "./index";
import {
  DATA_CONTEXT_TYPES,
  DATA_SOURCES,
  DATA_SUBJECTS,
  DATA_TABLE_DETAIL,
  DEFAULT_CONTEXT_TYPES,
  DEFAULT_SOURCES,
  DEFAULT_SUBJECTS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
} from "./constants";
import type { TableConfig, TableDetail } from "../../types/table";

type DropDownItem = { label: string; value: string };

const typedGetDropDownList = getDropDownList as <T>(options: {
  list: T[];
  getLabel: (item: T) => string;
}) => DropDownItem[];

export const useSignalHistorySelectors = () => {
  const { state } = useSignalHistory();

  // Table selectors
  const tableDetail = useMemo<TableDetail>(
    () => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
    [state],
  );

  const tableConfig = useMemo<TableConfig>(() => DEFAULT_TABLE_CONFIG, []);

  // Sources selectors
  type SourceItem = {
    source_id: string;
    detail: {
      source_name: string;
    };
  };

  const sourcesEnums = useMemo<SourceItem[]>(
    () =>
      (state[DATA_SOURCES] as SourceItem[]) ||
      (DEFAULT_SOURCES as SourceItem[]),
    [state],
  );

  const sourcesEnumsList = useMemo(() => {
    const data = sourcesEnums.map((item) => ({ ...item, id: item.source_id }));
    return typedGetDropDownList({
      list: data,
      getLabel: ({ detail }: SourceItem) => detail.source_name,
    });
  }, [sourcesEnums]);

  // Context types selectors
  type ContextTypeItem = {
    context_type: string;
    detail: {
      type_name: string;
    };
  };

  const contextTypesEnums = useMemo<ContextTypeItem[]>(
    () =>
      (state[DATA_CONTEXT_TYPES] as ContextTypeItem[]) ||
      (DEFAULT_CONTEXT_TYPES as ContextTypeItem[]),
    [state],
  );

  const contextTypesEnumsList = useMemo(() => {
    const data = contextTypesEnums.map((item) => ({
      ...item,
      id: item.context_type,
    }));
    return typedGetDropDownList({
      list: data,
      getLabel: ({ detail }: ContextTypeItem) => detail.type_name,
    });
  }, [contextTypesEnums]);

  // Subjects selectors
  type SubjectItem = {
    subject_type: string;
    detail: {
      subject_name: string;
    };
  };

  const subjectsEnums = useMemo<SubjectItem[]>(
    () =>
      (state[DATA_SUBJECTS] as SubjectItem[]) ||
      (DEFAULT_SUBJECTS as SubjectItem[]),
    [state],
  );

  const subjectsEnumsList = useMemo(() => {
    const data = subjectsEnums.map((item) => ({
      ...item,
      id: item.subject_type,
    }));
    return typedGetDropDownList({
      list: data,
      getLabel: ({ detail }: SubjectItem) => detail.subject_name,
    });
  }, [subjectsEnums]);

  return {
    tableDetail,
    tableConfig,
    sourcesEnums,
    sourcesEnumsList,
    contextTypesEnums,
    contextTypesEnumsList,
    subjectsEnums,
    subjectsEnumsList,
  };
};
