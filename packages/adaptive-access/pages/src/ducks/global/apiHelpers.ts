// apiHelpers.ts
import { getErrorMessageFromApiResponse } from "@zscaler/zui-component-library";
import { useGlobalContext, type Notification } from "./index";

export type ApiResponse<T = unknown> = T & Record<string, unknown>;

type ApiError = {
  apiErrorLevel?: string;
  [key: string]: any;
};

export const useApiHelpers = () => {
  const {
    showSuccessNotification,
    showWarningNotification,
    showErrorNotification,
  } = useGlobalContext();

  const apiSuccessNotifier =
    <T>(showNotification = true, payload: Notification = {}) =>
    (response: ApiResponse<T>): ApiResponse<T> => {
      if (showNotification && payload.message) {
        // Display success notification and attach ID to response
        const notificationId = showSuccessNotification({ ...payload });
        return { notificationId, ...response };
      }
      return response;
    };

  const apiErrorNotifier =
    (showNotification = true, payload: Notification = {}) =>
    async ({ error }: { error: ApiError }): Promise<never> => {
      if (showNotification) {
        const errorMessage = getErrorMessageFromApiResponse({
          error,
        }) as string;
        const showWarning = error?.apiErrorLevel === "WARN";

        if (errorMessage) {
          const notificationId = showWarning
            ? showWarningNotification({
                autoHide: false,
                ...payload,
                message: errorMessage,
              })
            : showErrorNotification({
                autoHide: false,
                ...payload,
                message: errorMessage,
              });

          return Promise.reject({ notificationId, ...error });
        }
      }
      return Promise.reject(error);
    };

  return {
    apiSuccessNotifier,
    apiErrorNotifier,
  };
};
