"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  type ReactNode,
} from "react";
import { http } from "../../utils/http";
import {
  API_ENDPOINT,
  DATA_TABLE_DETAIL,
  DATA_INNER_TABLE_DETAIL,
  DEFAULT_STATE,
  type DefaultState,
} from "./constants";
import type { GetListParams } from "../../types/table";
import { AxiosResponse } from "axios";

export type UpdateListPayload = {
  overrideList: Array<Record<string, unknown>>;
  nextPageToken?: string;
};

export type GetInnerListParams = {
  subjectType?: string;
  subjectId?: string;
  subjectName?: string;
  contextCount?: number;
  overrideCount?: number;
  isEmpty?: boolean;
};

type ApiResponse = {
  data?: Array<Record<string, unknown>>;
};

export type TypePayload = {
  tenantId?: string;
  subjectType?: string;
  subjectId?: string;
  contextType?: string;
  source?: string;
  subjectName?: string;
  userId?: string;
  sortkey?: string;
  summarySortkey?: string;
  isSystemRole?: boolean;
  isOverridable?: boolean;
  [key: string]: any;
};

type SubjectIdentifierState = DefaultState;

type SubjectIdentifierAction =
  | { type: "UPDATE_TABLE_DETAIL"; payload: Array<Record<string, unknown>> }
  | {
      type: "UPDATE_INNER_TABLE_DETAIL";
      payload: Array<Record<string, unknown>>;
    };

const SubjectIdentifierContext = createContext<
  | {
      state: SubjectIdentifierState;
      getList: (params?: GetListParams) => Promise<void>;
      getInnerList: (params?: GetInnerListParams) => Promise<void>;
      addRow: (payload: Record<string, unknown>) => Promise<void>;
      deleteRow: (payload: Record<string, unknown>) => Promise<void>;
      updateRow: (payload: Record<string, unknown>) => Promise<void>;
    }
  | undefined
>(undefined);

const subjectIdentifierReducer = (
  state: SubjectIdentifierState,
  action: SubjectIdentifierAction,
): SubjectIdentifierState => {
  switch (action.type) {
    case "UPDATE_TABLE_DETAIL": {
      return {
        ...state,
        [DATA_TABLE_DETAIL]: {
          ...state[DATA_TABLE_DETAIL],
          data: [...action.payload],
          totalRecord: action.payload.length,
          hasFetchedAllRecords: true,
        },
      };
    }
    case "UPDATE_INNER_TABLE_DETAIL": {
      return {
        ...state,
        [DATA_INNER_TABLE_DETAIL]: {
          ...state[DATA_INNER_TABLE_DETAIL],
          data: [...action.payload],
          totalRecord: action.payload.length,
          hasFetchedAllRecords: true,
        },
      };
    }
    default:
      return state;
  }
};

export const SubjectIdentifierProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(subjectIdentifierReducer, DEFAULT_STATE);

  const getList = useCallback(async (params?: GetListParams) => {
    let requestUrl = `${API_ENDPOINT}/summary/subject?`;

    if (params?.nextPageToken) {
      requestUrl += `&lastEvaluatedKey=${params.nextPageToken}`;
    }

    if (params?.name) {
      requestUrl += `&subjectName=${encodeURIComponent(params.name.trim())}`;
    }

    const response: AxiosResponse<UpdateListPayload> =
      await http.get(requestUrl);

    if (response?.data?.overrideList) {
      dispatch({
        type: "UPDATE_TABLE_DETAIL",
        payload: response.data.overrideList || [],
      });
    }
  }, []);

  const getInnerList = useCallback(
    async ({
      subjectType = "",
      subjectId = "",
      isEmpty = false,
    }: GetInnerListParams = {}) => {
      if (isEmpty) {
        dispatch({ type: "UPDATE_INNER_TABLE_DETAIL", payload: [] });
        return;
      }

      const requestUrl = `${API_ENDPOINT}/subjects/${subjectType}/${subjectId}`;
      const response: AxiosResponse<ApiResponse[]> = await http.get(requestUrl);

      if (response?.data) {
        dispatch({
          type: "UPDATE_INNER_TABLE_DETAIL",
          payload: response?.data || [],
        });
      }
    },
    [],
  );

  const addRow = useCallback(
    async (payload: TypePayload) => {
      const response = await http.post(API_ENDPOINT, payload);

      if (response?.data) {
        await getList();
      }
    },
    [getList],
  );

  const deleteRow = useCallback(
    async (payload: TypePayload) => {
      const deletePayload = {
        tenantId: payload.tenantId,
        subjectType: payload.subjectType,
        subjectId: payload.subjectId,
        contextType: payload.contextType,
        source: payload.source,
        subjectName: payload.subjectName,
        ...(payload.subjectType === "USER_DEVICE"
          ? { userId: payload.userId }
          : {}),
      };

      await http.delete(API_ENDPOINT, { data: deletePayload });

      await getInnerList({
        subjectType: payload.subjectType,
        subjectId: payload.subjectId,
      });
    },
    [getInnerList],
  );

  const updateRow = useCallback(
    async (payload: TypePayload) => {
      const updatedPayload = { ...payload };
      delete updatedPayload.sortkey;
      delete updatedPayload.summarySortkey;

      const response = await http.post(API_ENDPOINT, updatedPayload);

      if (response?.data) {
        await getInnerList({
          subjectType: payload.subjectType,
          subjectId: payload.subjectId,
        });
      }
    },
    [getInnerList],
  );

  return (
    <SubjectIdentifierContext.Provider
      value={{
        state,
        getList,
        getInnerList,
        addRow,
        deleteRow,
        updateRow,
      }}
    >
      {children}
    </SubjectIdentifierContext.Provider>
  );
};

export const useSubjectIdentifierContext = () => {
  const context = useContext(SubjectIdentifierContext);

  if (!context) {
    throw new Error(
      "useSubjectIdentifierContext must be used within SubjectIdentifierProvider",
    );
  }

  return context;
};
