"use client";

import React, {
  createContext,
  use<PERSON>ontext,
  useReducer,
  use<PERSON><PERSON>back,
  type ReactNode,
} from "react";
import { http } from "../../utils/http";
import type { GetListParams } from "../../types/table";
import type { FormValues } from "../../components/conditional-access-profile/helper";
import {
  API_ENDPOINT,
  DATA_DYNAMIC_FIELDS_DETAIL,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
} from "./constants";
import type {
  DynamicField,
  Profiles,
} from "../../components/conditional-access-profile/types/query-builder";
import { isArray } from "lodash-es";
import { AxiosResponse } from "axios";

// --- Types ---
type ProfileState = typeof DEFAULT_STATE;

type ProfileAction =
  | { type: "UPDATE_TABLE_DETAIL"; payload: Profiles[] }
  | { type: "UPDATE_DYNAMIC_FIELDS"; payload: Dynamic<PERSON>ield[] };

type ProfileContextType = {
  state: ProfileState;
  getList: (params: GetListParams) => Promise<void>;
  getDynamicFields: () => Promise<void>;
  add: (formValues: FormValues) => Promise<void>;
  update: (formValues: FormValues) => Promise<void>;
  deleteData: (formValues: FormValues) => Promise<void>;
};

// --- Context ---
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// --- Reducer ---
function profileReducer(
  state: ProfileState,
  action: ProfileAction,
): ProfileState {
  switch (action.type) {
    case "UPDATE_TABLE_DETAIL": {
      const { payload } = action;
      const tableDetail = { ...state[DATA_TABLE_DETAIL] };

      if (isArray(payload)) {
        tableDetail.totalRecord = payload.length;
        tableDetail.data = [...payload];
      }

      return {
        ...state,
        [DATA_TABLE_DETAIL]: tableDetail,
      };
    }

    case "UPDATE_DYNAMIC_FIELDS": {
      return {
        ...state,
        [DATA_DYNAMIC_FIELDS_DETAIL]: action.payload ?? [],
      };
    }

    default:
      return state;
  }
}

// --- Provider ---
export const ProfileProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(profileReducer, DEFAULT_STATE);

  const getList = useCallback(
    async ({
      pageSize = DEFAULT_STATE[DATA_TABLE_DETAIL].pageSize,
      pageOffset = DEFAULT_STATE[DATA_TABLE_DETAIL].pageOffset,
      requireTotal = true,
      name = "",
    }: GetListParams) => {
      try {
        let requestUrl = `${API_ENDPOINT}?limit=${pageSize}&offset=${pageOffset}`;
        if (requireTotal) requestUrl += `&requireTotal=${requireTotal}`;
        if (name) requestUrl += `&name=${encodeURIComponent(name.trim())}`;

        const response: AxiosResponse<Profiles[]> = await http.get(requestUrl);
        if (response?.data) {
          let filteredData = response.data;
          if (name.trim()) {
            const searchNameLower = name.trim().toLowerCase();
            filteredData = filteredData.filter((item) =>
              item.name.toLowerCase().includes(searchNameLower),
            );
          }

          dispatch({ type: "UPDATE_TABLE_DETAIL", payload: filteredData });
        }
      } catch (error) {
        console.error("Error fetching table data:", error);
      }
    },
    [],
  );

  const getDynamicFields = useCallback(async () => {
    try {
      const requestUrl = `${API_ENDPOINT}/operators`;
      const response: AxiosResponse<DynamicField[]> =
        await http.get(requestUrl);
      if (response?.data) {
        dispatch({ type: "UPDATE_DYNAMIC_FIELDS", payload: response.data });
      }
    } catch (error) {
      console.error("Error fetching dynamic fields:", error);
    }
  }, []);

  const add = useCallback(
    async (formValues: FormValues) => {
      try {
        await http.post(`${API_ENDPOINT}`, formValues);
        getList({}); // Refresh the table list after addition
      } catch (error) {
        console.error("Error adding profile:", error);
        throw error;
      }
    },
    [getList],
  );

  const update = useCallback(
    async (formValues: FormValues) => {
      try {
        await http.put(`${API_ENDPOINT}/${formValues.id}`, formValues);
        getList({}); // Refresh the table list after update
      } catch (error) {
        console.error("Error updating profile:", error);
        throw error;
      }
    },
    [getList],
  );

  const deleteData = useCallback(
    async (formValues: FormValues) => {
      try {
        await http.delete(`${API_ENDPOINT}/${formValues.id}`);
        getList({}); // Refresh the table list after deletion
      } catch (error) {
        console.error("Error deleting profile:", error);
        throw error;
      }
    },
    [getList],
  );

  return (
    <ProfileContext.Provider
      value={{
        state,
        getList,
        getDynamicFields,
        add,
        update,
        deleteData,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};

// --- Hook ---
export const useProfileContext = (): ProfileContextType => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error("useProfileContext must be used within a ProfileProvider");
  }
  return context;
};
