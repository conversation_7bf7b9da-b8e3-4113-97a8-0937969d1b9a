import dayjs from "dayjs";
import {
  type TableColumnDetail,
  type TableConfig,
  type TableDetail,
} from "../../types/table";
import { DynamicField } from "../../components/conditional-access-profile/types/query-builder";

export const REDUCER_KEY = "conditional-access-profile";

export const API_ENDPOINT = "/manager/v1/profiles";

export const DATA_TABLE_DETAIL = "tableDetail";

export const DATA_DYNAMIC_FIELDS_DETAIL = "dynamicFields";

type RowData = {
  name?: string;
  desc?: string;
  modifiedAt?: number;
  conditions?: string;
};

const DEFAULT_TABLE_COLUMNS_DETAILS: TableColumnDetail[] = [
  {
    id: "name",
    accessorFn: (row: RowData) => row.name ?? "-",
    Header: "PROFILE_NAME",
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "desc",
    accessorFn: (row: RowData) => row.desc ?? "-",
    Header: "DESCRIPTION",
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "modifiedAt",
    accessorFn: (row: RowData) =>
      row.modifiedAt
        ? dayjs(row.modifiedAt).format("MMMM DD, YYYY - hh:mm A")
        : "-",
    Header: "LAST_MODIFIED_ON",
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "conditions",
    accessorFn: (row: RowData) => row.conditions ?? "-",
    Header: "CRITERIA",
    minSize: 400,
    size: 400,
    defaultCanSort: true,
    sortingFn: "alphanumeric",
  },
  {
    id: "actions",
    Header: "ACTIONS",
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
  },
];

export const DEFAULT_TABLE_CONFIG: TableConfig = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: "modifiedAt" }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL: TableDetail = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_DYNAMIC_FIELDS_DETAIL: DynamicField[] = [];

export type DefaultState = {
  [DATA_TABLE_DETAIL]: TableDetail;
  [DATA_DYNAMIC_FIELDS_DETAIL]: DynamicField[];
};

export const DEFAULT_STATE: DefaultState = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_DYNAMIC_FIELDS_DETAIL]: DEFAULT_DYNAMIC_FIELDS_DETAIL,
};
