"use client";

import React, {
  createContext,
  useReducer,
  use<PERSON><PERSON>back,
  useContext,
  type ReactNode,
} from "react";
import { isArray } from "lodash-es";
import { http } from "../../utils/http";
import {
  API_ENDPOINT,
  DATA_TABLE_DETAIL,
  DATA_INNER_TABLE_DETAIL,
  DEFAULT_STATE,
  type DefaultState,
} from "./constants";
import { TableDetail } from "../../types/table";
import { TypePayload } from "../subject-identifier";

type ContextTypeState = DefaultState;

type ContextTypeAction =
  | {
      type: "UPDATE_TABLE_DETAIL";
      payload: {
        records: Array<Record<string, unknown>>;
        pageSize: number;
        pageOffset: number;
        totalRecord: number;
      };
    }
  | {
      type: "UPDATE_INNER_TABLE_DETAIL";
      payload: {
        records: Array<Record<string, unknown>>;
        pageSize: number;
        pageOffset: number;
        totalRecord: number;
      };
    };

const ContextTypeContext = createContext<{
  state: ContextTypeState;
  getList: () => Promise<void>;
  getInnerList: () => Promise<void>;
  add: (payload: TypePayload) => Promise<void>;
  deleteData: (payload: TypePayload) => Promise<void>;
  update: (payload: TypePayload) => Promise<void>;
} | null>(null);

const contextTypeReducer = (
  state: ContextTypeState,
  action: ContextTypeAction,
): ContextTypeState => {
  switch (action.type) {
    case "UPDATE_TABLE_DETAIL": {
      const { records, pageSize, pageOffset, totalRecord } = action.payload;
      const tableDetail: TableDetail = { ...state[DATA_TABLE_DETAIL] };

      if (isArray(records)) {
        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;
        tableDetail.totalRecord = totalRecord;
        tableDetail.data = [...tableDetail.data, ...records];

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }
      }

      return { ...state, [DATA_TABLE_DETAIL]: tableDetail };
    }

    case "UPDATE_INNER_TABLE_DETAIL": {
      const { records, pageSize, pageOffset, totalRecord } = action.payload;
      const innerTableDetail: TableDetail = {
        ...state[DATA_INNER_TABLE_DETAIL],
      };

      if (isArray(records)) {
        innerTableDetail.pageSize = pageSize;
        innerTableDetail.pageOffset = pageOffset;
        innerTableDetail.totalRecord = totalRecord;
        innerTableDetail.data = [...records];

        if (innerTableDetail.totalRecord === innerTableDetail.data.length) {
          innerTableDetail.hasFetchedAllRecords = true;
        }
      }

      return { ...state, [DATA_INNER_TABLE_DETAIL]: innerTableDetail };
    }

    default:
      return state;
  }
};

export const ContextTypeProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(contextTypeReducer, DEFAULT_STATE);

  /**
   * Function: Get List Data
   */
  const getList = useCallback(async () => {
    const dummyData = {
      records: [
        {
          contextType: "ITDR User Risk Score",
          source: "Zscaler Internet Access",
          subjectCount: 3,
          overrideCount: 1,
        },
        {
          contextType: "ABCD User Risk Score",
          source: "Zscaler Private Access",
          subjectCount: 5,
          overrideCount: 4,
        },
        {
          contextType: "XYZS User Risk Score",
          source: "Zscaler Client Connector",
          subjectCount: 7,
          overrideCount: 9,
        },
      ],
      pageSize: 100,
      pageOffset: 0,
      totalRecord: 3,
    };

    dispatch({ type: "UPDATE_TABLE_DETAIL", payload: dummyData });
  }, []);

  /**
   * Function: Get Inner List Data
   */
  const getInnerList = useCallback(async () => {
    const dummyData = {
      records: [
        {
          subjectIdentifier: "<EMAIL>",
          contextValue: "Critical",
          contextExpiry: "Feb 15th, 2024",
        },
        {
          subjectIdentifier: "<EMAIL>",
          contextValue: "Critical",
          contextExpiry: "Feb 15th, 2024",
        },
        {
          subjectIdentifier: "<EMAIL>",
          contextValue: "Critical",
          contextExpiry: "Feb 15th, 2024",
        },
      ],
      pageSize: 100,
      pageOffset: 0,
      totalRecord: 3,
    };

    dispatch({ type: "UPDATE_INNER_TABLE_DETAIL", payload: dummyData });
  }, []);

  /**
   * Function: Add Data
   */
  const add = useCallback(
    async (payload: TypePayload) => {
      const response = await http.post(API_ENDPOINT, payload);

      if (response?.data) {
        await getList();
      }
    },
    [getList],
  );

  /**
   * Function: Delete Data
   */
  const deleteData = useCallback(
    async (payload: TypePayload) => {
      await http.delete(`${API_ENDPOINT}/${payload.id}`);
      await getList();
    },
    [getList],
  );

  /**
   * Function: Update Data
   */
  const update = useCallback(
    async (payload: TypePayload) => {
      const response = await http.put(`${API_ENDPOINT}/${payload.id}`, payload);

      if (response?.data) {
        await getList();
      }
    },
    [getList],
  );

  return (
    <ContextTypeContext.Provider
      value={{
        state,
        getList,
        getInnerList,
        add,
        deleteData,
        update,
      }}
    >
      {children}
    </ContextTypeContext.Provider>
  );
};

export const useContextType = () => {
  const context = useContext(ContextTypeContext);

  if (!context) {
    throw new Error("useContextType must be used within ContextTypeProvider");
  }

  return context;
};
