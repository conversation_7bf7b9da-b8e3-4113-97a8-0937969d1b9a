import { render, type RenderOptions } from "@testing-library/react";
import { type ReactElement, type ReactNode } from "react";
import StoreProvider from "../StoreProvider";

type AllTheProvidersProps = {
  children?: ReactNode;
};

const AllTheProviders = ({ children }: AllTheProvidersProps): JSX.Element => (
  <StoreProvider>{children}</StoreProvider>
);

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, "wrapper">,
) => render(ui, { wrapper: AllTheProviders, ...options });

export { customRender as render };
