import { get } from "lodash-es";

type ErrorResponse = {
  response?: {
    status?: number;
    data?: unknown;
  };
};

export const handleError = (error: ErrorResponse): Promise<never> => {
  const errorStatus = get(error, "response.status", 0);
  const errorData = get(error, "response.data", "");

  // Skip checking request URL as we use one identity login portal
  if (errorStatus === 401) {
    window?.handleLogout?.();
  }

  return Promise.reject({ errorStatus, error: errorData, raw: error });
};
