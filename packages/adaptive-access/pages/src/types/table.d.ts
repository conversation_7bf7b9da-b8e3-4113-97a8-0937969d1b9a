export type TableColumnDetail = {
  cell?: any;
  id: string;
  accessorFn?: (params: T) => unknown;
  Header: string;
  minSize: number;
  size: number;
  defaultCanSort: boolean;
  sortingFn?: string;
  disableSortBy?: boolean;
  sortType?: string;
  enableResizing?: boolean;
};

export type TableConfig = {
  columns: TableColumnDetail[];
  initialState: {
    sortBy: Array<{ id: string }>;
    hiddenColumns?: [];
  };
  onFiltersApply: () => null;
  showColumnLayoutConfigurer: boolean;
  showRowTooltip: boolean;
};

export type TableDetail = {
  totalRecord?: number;
  pageSize?: number;
  pageOffset?: number;
  hasFetchedAllRecords: boolean;
  data: Array<Record<string, unknown>>;
  hasData: boolean;
  hasError: boolean;
  error: Record<string, unknown>;
  nextPageToken?: string | number;
  name?: string;
};

export type UpdateTableDetailPayload = {
  result?: Array<Record<string, unknown>>;
  pageSize?: number;
  pageOffset?: number;
  total?: number;
  totalRecord?: number;
  records?: Array<Record<string, unknown>>;
};

export type GetTimeRangeFunction = (level: string) => string[];

export type GetListParams = {
  pageSize?: number;
  pageOffset?: number;
  requireTotal?: boolean;
  name?: string;
  nextPageToken?: string;
  filters?: {
    where?: string;
    isDateRangeValid?: boolean;
    [key: string]: unknown;
  };
};
