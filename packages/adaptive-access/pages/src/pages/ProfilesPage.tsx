import React, { useEffect } from "react";
import NoAccess from "../components/no-access/NoAccess";
import Actions from "../components/conditional-access-profile/Actions";
import CRUD from "../components/conditional-access-profile/CRUD";
import Table from "../components/conditional-access-profile/Table";
import { usePermissions } from "../ducks/permissions";
import { PERMISSIONS_KEY } from "../config";
import CRUDPageContextProvider from "../contexts/CRUDPageContextProvider";
import { useProfileContext } from "../ducks/conditional-access-profile";

const ProfilesPage: React.FC = () => {
  const { getList, getDynamicFields } = useProfileContext();
  const { getPermissionsByKey, getAllPermissions } = usePermissions();

  const privileges = getPermissionsByKey(PERMISSIONS_KEY.ROLES_POLICY);
  const { noAccess } = privileges;

  useEffect(() => {
    getDynamicFields();
    getList({});
    getAllPermissions();
  }, [getDynamicFields, getList, getAllPermissions]);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <>
      <CRUDPageContextProvider privileges={privileges}>
        <Actions />
        <Table />
        <CRUD />
      </CRUDPageContextProvider>
    </>
  );
};

export default ProfilesPage;
