"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import type React from "react";
import { useTranslation } from "react-i18next";
import { faSync } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  Button,
  DropDown,
  Search,
  TableContainer,
  TextWithTooltip,
  getCalendarDDList,
} from "@zscaler/zui-component-library";
import Filters from "../components/signal-history/Filters";
import { getFilterApiPayload } from "../components/signal-history/helper";
import NoAccess from "../components/no-access/NoAccess";
import { PERMISSIONS_KEY } from "../config";
import type { FilterOption, TimeRange } from "../types/dropdown";
import type { SelectedFilter } from "../types/filters";
import { usePermissions } from "../ducks/permissions";
import { useApiCall } from "../hooks/useApiCallContext";
import { noop } from "lodash-es";
import { useSignalHistory } from "../ducks/signal-history";
import { useSignalHistorySelectors } from "../ducks/signal-history/selectors";
import { GetListParams } from "../types/table";
import { useGlobalContext } from "../ducks/global";

const columnConfigIgnoreList = ["number"];

const searchOptions: FilterOption[] = [
  { label: "SUBJECT", value: "SUBJECT" },
  { label: "SOURCE", value: "SOURCE" },
  { label: "CONTEXT_TYPE", value: "CONTEXT_TYPE" },
];

const getDefaultTimeRange = (): TimeRange[] => {
  const newList = getCalendarDDList({}) as TimeRange[];
  return [newList[0]] as TimeRange[];
};

const defaultSelectedItem: [FilterOption] = [{ label: "ALL", value: "ALL" }];

const defaultSelectedFilter = (): SelectedFilter => ({
  timeRange: getDefaultTimeRange(),
  subjects: [defaultSelectedItem[0]],
  contextTypes: [defaultSelectedItem[0]],
  sources: [defaultSelectedItem[0]],
});

const SignalHistoryPage: React.FC = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const { showErrorNotification } = useGlobalContext();
  const { getPermissionsByKey, getAllPermissions } = usePermissions();
  const { getList } = useSignalHistory();
  const { tableConfig, tableDetail } = useSignalHistorySelectors();

  const privileges = getPermissionsByKey(PERMISSIONS_KEY.AUDIT_LOGS_POLICY);
  const { noAccess } = privileges;

  const [selectedFilter, setSelectedFilter] = useState<SelectedFilter>(
    defaultSelectedFilter(),
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSearchField, setSelectedSearchField] = useState(
    searchOptions[0],
  );

  // Use useRef for mutable flags that don't trigger re-renders
  const isSearching = useRef(false);
  const isLoading = useRef(false);
  const initialRenderRef = useRef(true);

  type RowData = {
    isSystemRole?: boolean;
    name?: string;
    subject?: string;
    subject_identifier?: string;
    [key: string]: unknown;
  };

  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail) => {
        if (columnDetail.id === "subject") {
          const TextWithTooltipComponent = (props: {
            row: { original: RowData };
          }) => {
            const { subject, subject_identifier } = props.row.original || {};

            return (
              <TextWithTooltip
                text={`${subject} ${
                  subject_identifier && subject_identifier !== "null"
                    ? `( ${subject_identifier} )`
                    : ""
                }`}
              />
            );
          };

          return { ...columnDetail, cell: TextWithTooltipComponent };
        }

        if (columnDetail.id === "source_name") {
          const TextWithTooltipComponent = (props: {
            row: { original: RowData };
          }) => {
            const { source_name } = props.row.original || {};

            return <TextWithTooltip text={source_name ?? ""} />;
          };

          return { ...columnDetail, cell: TextWithTooltipComponent };
        }

        return columnDetail;
      }) || [],
    [tableConfig?.columns],
  );

  const getSearchField = useCallback(() => {
    const { value } = selectedSearchField ?? {};
    if (value === "SUBJECT") return "subject";
    if (value === "SOURCE") return "source_name";
    if (value === "CONTEXT_TYPE") return "context_type";

    return "";
  }, [selectedSearchField]);

  const onRefreshClick = useCallback(() => {
    setSearchTerm("");
    setSelectedSearchField(searchOptions[0]);
    setSelectedFilter(defaultSelectedFilter());
  }, []);

  const fetchData = useCallback(
    (
      options: {
        term?: string;
        loadMore?: boolean;
      } = {},
    ) => {
      if (isLoading.current) return; // Prevent overlapping API calls

      const { term = searchTerm, loadMore = false } = options;
      const filters = getFilterApiPayload({ selectedFilter });
      const searchField = getSearchField();

      const payload: GetListParams = {
        filters: { ...filters },
      };

      if (loadMore) {
        payload.pageSize = tableDetail.pageSize;
        payload.pageOffset =
          (tableDetail.pageOffset ?? 0) + (tableDetail.pageSize ?? 0);
      }

      if (searchField && term) {
        payload.filters = {
          ...payload.filters,
          where: `${
            filters.where ? filters.where + " and " : ""
          }${searchField} starts_with '${term}'`,
        };
      }

      if (payload?.filters?.isDateRangeValid) {
        isLoading.current = true;
        apiCall(() => getList(payload))
          .then(() => {
            isLoading.current = false;
          })
          .catch(() => {
            isLoading.current = false;
          });
      } else {
        showErrorNotification({ message: "START_TIME_BEFORE_END_TIME" });
      }
    },
    [
      searchTerm,
      selectedFilter,
      getSearchField,
      tableDetail.pageSize,
      tableDetail.pageOffset,
      getList,
      showErrorNotification,
    ],
  );

  const onSearchEnter = useCallback(
    (term: string) => {
      isSearching.current = true;
      setSearchTerm(term);
      fetchData({ term });
    },
    [fetchData],
  );

  useEffect(() => {
    apiCall(() => getAllPermissions()).catch(noop);
  }, []);

  // Fixed useEffect to prevent infinite API calls
  useEffect(() => {
    // Skip the first render
    if (initialRenderRef.current) {
      initialRenderRef.current = false;
      fetchData();
      return;
    }

    // Only fetch data when selectedFilter changes intentionally
    if (!isSearching.current && !isLoading.current) {
      fetchData();
    }

    isSearching.current = false;
  }, [selectedFilter]); // Remove fetchData from dependencies!

  const onLoadMoreClick = () => {
    fetchData({ loadMore: true });
  };

  const renderFilterSection = () => (
    <div style={{ display: "flex" }}>
      <Filters
        selectedFilter={selectedFilter}
        setSelectedFilter={setSelectedFilter}
      />
      <div
        style={{ flex: 1, justifyContent: "flex-end" }}
        className="is-flex has-ai-c"
      >
        <Button
          type="tertiary"
          onClick={onRefreshClick}
          containerClass="no-p-l content-width"
          style={{ paddingRight: "8px", marginRight: "8px", marginLeft: "8px" }}
        >
          <FontAwesomeIcon icon={faSync} />
        </Button>
        <div className="dropdown-with-search">
          <DropDown
            list={searchOptions}
            selectedList={[selectedSearchField]}
            onSelection={(payload: typeof searchOptions) => {
              setSelectedSearchField(payload[0]);
            }}
            selectedItemsProps={{
              kind: "tertiary",
              containerStyle: {
                justifyContent: "center",
                paddingLeft: "4px",
                paddingRight: "4px",
                minWidth: "70px",
              },
            }}
            itemsSelectionProps={{
              containerStyle: {
                minWidth: "130px",
              },
            }}
          />
          <Search
            onSearch={onSearchEnter}
            term={searchTerm}
            containerClass="no-m-r"
            containerStyle={{ width: "100%" }}
          />
        </div>
      </div>
    </div>
  );

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <>
      <section className="heading-small page-title">
        {t("SIGNAL_HISTORY")}
      </section>
      {renderFilterSection()}
      <TableContainer
        {...tableConfig}
        columns={tableColumnConfig}
        data={tableDetail.data}
        containerClass="full-width"
        pagination={{ ...tableDetail, onLoadMoreClick }}
        columnConfigIgnoreList={columnConfigIgnoreList}
      />
    </>
  );
};

export default SignalHistoryPage;
