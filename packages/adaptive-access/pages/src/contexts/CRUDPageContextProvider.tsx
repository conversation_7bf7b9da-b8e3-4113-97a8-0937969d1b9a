import type React from "react";
import { createContext } from "react";
import { useCRUDPageContext } from "../hooks/useCRUDPageContext";
import {
  type CRUDPageContextProviderProps,
  type CRUDPageContextValue,
} from "../types/crudcontext";

export const CRUDPageContext = createContext<CRUDPageContextValue | undefined>(
  undefined,
);

const CRUDPageContextProvider: React.FC<CRUDPageContextProviderProps> = ({
  defaultModalMode,
  defaultDetail,
  defaultBulkActionOption,
  defaultSearchOption,
  privileges,
  children,
}) => {
  const contextValue = useCRUDPageContext({
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultSearchOption,
    privileges,
  });

  return (
    <CRUDPageContext.Provider value={contextValue}>
      {children}
    </CRUDPageContext.Provider>
  );
};

export default CRUDPageContextProvider;
