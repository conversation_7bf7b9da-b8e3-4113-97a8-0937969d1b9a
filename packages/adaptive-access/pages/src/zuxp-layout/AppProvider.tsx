// AppProvider.tsx
"use client";

import { type ReactNode, useEffect, useState } from "react";
import { I18nextProvider } from "react-i18next";
import i18next from "i18next";
import { getAppID, getPortalID } from "../config";
import AppLayout from "./AppLayout";
import StyleProvider from "./StyleProvider";
import { PermissionsProvider } from "../ducks/permissions";
import { GlobalProvider } from "../ducks/global";
import { SetupInitializer } from "../ducks/global/SetupInitializer";
import { setupApiMethodMessageOption } from "../ducks/global/setupWithContext";
import { FeatureFlagsProvider, FeatureFlagsType } from "./FeatureFlagsProvider";

type AppProviderProps = {
  children: ReactNode;
  featureFlags: FeatureFlagsType; // Use refined type
};

const AppProvider = ({ children, featureFlags }: AppProviderProps) => {
  useEffect(() => {
    setupApiMethodMessageOption();
  }, []);

  return (
    <>
      <div id={getAppID()}>
        <I18nextProvider i18n={i18next}>
          <StyleProvider>
            <GlobalProvider>
              <FeatureFlagsProvider featureFlags={featureFlags}>
                <SetupInitializer rootId={getPortalID()}>
                  <PermissionsProvider>
                    <AppLayout>{children}</AppLayout>
                  </PermissionsProvider>
                </SetupInitializer>
              </FeatureFlagsProvider>
            </GlobalProvider>
          </StyleProvider>
        </I18nextProvider>
      </div>
      <div id={getPortalID()} />
    </>
  );
};

export default AppProvider;
