"use client";

import type React from "react";

import { useEffect, useCallback } from "react";
import {
  LoaderContainer,
  ToastContainer,
} from "@zscaler/zui-component-library";
import { noop } from "lodash-es";
import { usePermissions } from "../ducks/permissions";
import { useGlobalContext } from "../ducks/global";

type AppLayoutProps = {
  children: React.ReactNode;
};

const AppLayout = ({ children }: AppLayoutProps) => {
  const {
    state: globalState,
    appSetupDone,
    showLoader,
    hideLoader,
    hideNotification,
  } = useGlobalContext();
  const { getMyPermissions } = usePermissions();

  const isLoading = globalState.loading;
  const notificationList = Object.values(globalState.notifications || {});
  const isAppSetupDone = globalState.appState === "SETUP_DONE";
  const appSetupPending = globalState.appState === "NOT_SET";

  const setupAuth = useCallback(async () => {
    showLoader();

    try {
      await getMyPermissions();
    } catch (error) {
      console.error("Error setting up auth:", error);
      noop();
    } finally {
      appSetupDone();
      hideLoader();
    }
  }, [showLoader, getMyPermissions, appSetupDone, hideLoader]);

  useEffect(() => {
    if (appSetupPending) {
      void setupAuth();
    }
  }, [appSetupPending, setupAuth]);

  const renderRoutesSection = () => {
    if (!isAppSetupDone) {
      return null;
    }

    return children;
  };

  const onNotificationClose = (id: number) => {
    hideNotification(id);
  };

  return (
    <>
      <LoaderContainer isLoading={isLoading} />
      <ToastContainer list={notificationList} onClose={onNotificationClose} />
      {renderRoutesSection()}
    </>
  );
};

export default AppLayout;
