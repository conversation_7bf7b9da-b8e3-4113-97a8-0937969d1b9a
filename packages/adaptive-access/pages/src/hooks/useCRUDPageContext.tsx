import { useEffect, useMemo, useState } from "react";
import {
  type BulkActionOption,
  type CRUDPageContextValue,
  type CsvImportResultDetail,
  type UseCRUDPageContextProps,
} from "../types/crudcontext";
import { type Privileges } from "../types/permissions";

const DEFAULT_MODAL_MODE = "";
const DEFAULT_DETAIL = {};
const DEFAULT_BULK_ACTION_OPTION: BulkActionOption = {
  label: "ACTIONS",
  value: "ACTIONS",
};
const DEFAULT_SEARCH_OPTION = {};
const DEFAULT_CSV_IMPORT_DETAIL = {};
const DEFAULT_CSV_IMPORT_RESULT_DETAIL: CsvImportResultDetail | null = null;
const DEFAULT_PRIVILEGES: Privileges = {
  hasFullAccess: false,
  noAccess: false,
};

export const useCRUDPageContext = ({
  defaultModalMode = DEFAULT_MODAL_MODE,
  defaultDetail = DEFAULT_DETAIL,
  defaultBulkActionOption = DEFAULT_BULK_ACTION_OPTION,
  defaultSearchOption = DEFAULT_SEARCH_OPTION,
  defaultCsvImportDetail = DEFAULT_CSV_IMPORT_DETAIL,
  defaultCsvImportResultDetail = DEFAULT_CSV_IMPORT_RESULT_DETAIL,
  privileges = DEFAULT_PRIVILEGES,
}: UseCRUDPageContextProps = {}): CRUDPageContextValue => {
  const [modalMode, setModalMode] = useState<string | boolean>(
    defaultModalMode,
  );
  const [isFormReadOnly, setIsFormReadOnly] = useState<boolean>(false);
  const [detail, setDetail] = useState<Record<string, unknown>>(defaultDetail);
  const [selectedRowDetail, setSelectedRowDetail] = useState<unknown[]>([]);
  const [selectedBulkAction, setSelectedBulkAction] =
    useState<BulkActionOption>(defaultBulkActionOption);
  const [selectedSearchField, setSelectedSearchField] =
    useState<Record<string, unknown>>(defaultSearchOption);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [csvImportDetail, setCsvImportDetail] = useState<
    Record<string, unknown>
  >(defaultCsvImportDetail);
  const [csvImportResult, setCsvImportResult] =
    useState<CsvImportResultDetail | null>(defaultCsvImportResultDetail);

  useEffect(() => {
    const { hasFullAccess } = privileges;

    if (modalMode === "view" || !hasFullAccess) {
      setIsFormReadOnly(true);
    } else {
      setIsFormReadOnly(false);
    }
  }, [modalMode, privileges]);

  const contextValue = useMemo(
    () => ({
      modalMode,
      setModalMode,
      isFormReadOnly,
      setIsFormReadOnly,
      detail,
      setDetail,
      selectedRowDetail,
      setSelectedRowDetail,
      selectedBulkAction,
      setSelectedBulkAction,
      selectedSearchField,
      setSelectedSearchField,
      searchTerm,
      setSearchTerm,
      csvImportDetail,
      setCsvImportDetail,
      csvImportResult,
      setCsvImportResult,
      defaultModalMode,
      defaultDetail,
      defaultBulkActionOption,
      defaultSearchOption,
      defaultCsvImportDetail,
      defaultCsvImportResultDetail,
      privileges,
    }),
    [
      modalMode,
      setModalMode,
      isFormReadOnly,
      setIsFormReadOnly,
      detail,
      setDetail,
      selectedRowDetail,
      setSelectedRowDetail,
      selectedBulkAction,
      setSelectedBulkAction,
      selectedSearchField,
      setSelectedSearchField,
      searchTerm,
      setSearchTerm,
      csvImportDetail,
      setCsvImportDetail,
      csvImportResult,
      setCsvImportResult,
      defaultModalMode,
      defaultDetail,
      defaultBulkActionOption,
      defaultSearchOption,
      defaultCsvImportDetail,
      defaultCsvImportResultDetail,
      privileges,
    ],
  );

  return contextValue;
};
