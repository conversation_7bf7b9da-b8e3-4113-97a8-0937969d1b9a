"use client";

import { useCallback, useEffect, useRef } from "react";
import { useGlobalContext } from "../ducks/global";

export const useApiCall = (
  options: {
    hasLoader?: boolean;
    hasNotification?: boolean;
    clearNotification?: boolean;
    clearNotificationOnNextCall?: boolean;
    clearLoader?: boolean;
    errorNotificationPayload?: Record<string, unknown>;
    successNotificationPayload?: Record<string, unknown>;
  } = {},
) => {
  const defaultConfig = {
    hasLoader: true,
    hasNotification: true,
    clearNotification: false,
    clearNotificationOnNextCall: false,
    clearLoader: true,
    errorNotificationPayload: {},
    successNotificationPayload: {},
  };

  // Merge default config with provided options
  const {
    hasLoader,
    hasNotification,
    clearNotification,
    clearNotificationOnNextCall,
    clearLoader,
    errorNotificationPayload,
    successNotificationPayload,
  } = {
    ...defaultConfig,
    ...options,
  };

  const {
    showLoader,
    hideLoader,
    hideNotification,
    showErrorNotification,
    showSuccessNotification,
  } = useGlobalContext();

  // Store notification ID
  const notificationIdRef = useRef<number | string | undefined>(undefined);

  const cleanupNotification = useCallback(() => {
    if (notificationIdRef.current && clearNotification) {
      hideNotification(notificationIdRef.current);
      notificationIdRef.current = undefined;
    }
  }, [clearNotification, hideNotification]);

  const cleanupLoader = useCallback(() => {
    if (clearLoader) {
      hideLoader();
    }
  }, [clearLoader, hideLoader]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      cleanupLoader();
      cleanupNotification();
    };
  }, [cleanupLoader, cleanupNotification]);

  const apiCall = useCallback(
    async <T,>(
      apiCallFunction: () => Promise<T>,
      config: {
        successMessage?: string;
        successNotificationPayload?: Record<string, unknown>;
        errorNotificationPayload?: Record<string, unknown>;
        hasLoader?: boolean;
        hasNotification?: boolean;
        [key: string]: any;
      } = {},
    ): Promise<T> => {
      // Apply config overrides
      const showLoaderForCall =
        config.hasLoader !== undefined ? config.hasLoader : hasLoader;
      const showNotificationForCall =
        config.hasNotification !== undefined
          ? config.hasNotification
          : hasNotification;

      if (showLoaderForCall) {
        showLoader();
      }

      if (clearNotificationOnNextCall && notificationIdRef.current) {
        hideNotification(notificationIdRef.current);
        notificationIdRef.current = undefined;
      }

      try {
        const result = await apiCallFunction();

        // Show success notification if message is provided or successNotificationPayload is provided
        if (
          showNotificationForCall &&
          (config.successMessage || config.successNotificationPayload)
        ) {
          const mergedSuccessPayload = {
            ...successNotificationPayload,
            ...config.successNotificationPayload,
          };

          // Use successMessage if provided, otherwise use default message (which might come from payload)
          const notificationOptions = config.successMessage
            ? { message: config.successMessage, ...mergedSuccessPayload }
            : mergedSuccessPayload;

          showSuccessNotification(notificationOptions);
        }

        return result;
      } catch (error: any) {
        console.error("API call error:", error);

        // Show error notification
        // if (showNotificationForCall) {
        //   const mergedErrorPayload = {
        //     ...errorNotificationPayload,
        //     ...config.errorNotificationPayload,
        //   };

        //   const errorMessage =
        //     error?.error || error?.message || error?.msg || "An error occurred";

        //   notificationIdRef.current = showErrorNotification({
        //     message: errorMessage,
        //     ...mergedErrorPayload,
        //   });
        // }

        throw error;
      } finally {
        if (showLoaderForCall) {
          hideLoader();
        }
      }
    },
    [
      hasLoader,
      hasNotification,
      clearNotification,
      clearNotificationOnNextCall,
      clearLoader,
      errorNotificationPayload,
      successNotificationPayload,
      showLoader,
      hideLoader,
      showSuccessNotification,
      showErrorNotification,
      hideNotification,
    ],
  );

  return {
    apiCall,
    cleanupNotification,
    cleanupLoader,
  };
};
