import dayjs from "dayjs";
import {
  type CalendarConfig,
  type FilterOption,
  type TimeRange,
} from "../../types/dropdown";
import {
  type FilterApiPayload,
  type SelectedFilter,
} from "../../types/filters";

export const calendarConfig: CalendarConfig = {
  minDate: dayjs().subtract(6, "M"),
  maxInterval: dayjs().diff(dayjs().subtract(6, "M"), "seconds"),
  message: { text: "Logs are available for the last 6 Months" },
};

export const getFilterApiPayload = ({
  selectedFilter,
}: {
  selectedFilter: SelectedFilter;
}): FilterApiPayload => {
  const filterApiPayload: FilterApiPayload = {};

  Object.keys(selectedFilter).forEach((filterName) => {
    const filterDetail = selectedFilter[filterName];

    if (Array.isArray(filterDetail) && filterDetail.length > 0) {
      if (filterName === "timeRange") {
        const { startTime, endTime } = (filterDetail as TimeRange[])[0] || {};
        filterApiPayload.start = dayjs(startTime).format(
          "YYYY-MM-DDTHH:mm:ss.SSSZ",
        );
        filterApiPayload.end = dayjs(endTime).format(
          "YYYY-MM-DDTHH:mm:ss.SSSZ",
        );
        filterApiPayload.isDateRangeValid =
          filterApiPayload.end > filterApiPayload.start;

        return;
      }

      filterApiPayload.where = `${
        filterApiPayload.where ? filterApiPayload.where + " and " : ""
      }`;

      (filterDetail as FilterOption[]).forEach(({ value }, index) => {
        if (value !== "ALL") {
          filterApiPayload.where += `${filterName}='${value}'${
            filterDetail.length - 1 === index ? "" : " or "
          }`;
        }
      });
    }
  });

  return filterApiPayload;
};
