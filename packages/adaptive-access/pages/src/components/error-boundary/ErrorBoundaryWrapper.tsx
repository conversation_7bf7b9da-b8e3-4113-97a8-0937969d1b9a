import { Component, type ReactNode, type ErrorInfo } from "react";

type ErrorBoundaryWrapperProps = {
  children: ReactNode;
  fallback?: (error: Error | null, errorInfo: ErrorInfo | null) => ReactNode;
};

type ErrorBoundaryWrapperState = {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
};

class ErrorBoundaryWrapper extends Component<
  ErrorBoundaryWrapperProps,
  ErrorBoundaryWrapperState
> {
  constructor(props: ErrorBoundaryWrapperProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(): Partial<ErrorBoundaryWrapperState> {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // You can log the error to an error reporting service
    console.error("Uncaught error:", error, errorInfo);
    this.setState({ error, errorInfo });

    // Here you could send the error to your error tracking service
    // For example, if using Sentry:
    // Sentry.captureException(error);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // render any custom fallback UI
      return this.props.fallback ? (
        this.props.fallback(this.state.error, this.state.errorInfo)
      ) : (
        <div>
          <h1>Oops! Something went wrong.</h1>
          <p>We are sorry for the inconvenience. Please try again later.</p>
          {process.env.NODE_ENV === "development" && (
            <details style={{ whiteSpace: "pre-wrap" }}>
              {this.state.error?.toString()}
              <br />
              {this.state.errorInfo?.componentStack}
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundaryWrapper;
