import { useEffect } from "react";

import { noop } from "lodash-es";
import CRUDPageContextProvider from "../../../contexts/CRUDPageContextProvider";
import NoAccess from "../../no-access/NoAccess";
import { type Privileges } from "../../../types/permissions";
import Table from "./Table";
import CRUD from "./CRUD";
import { usePermissions } from "../../../ducks/permissions";
import { useContextType } from "../../../ducks/context-type";
import { useApiCall } from "../../../hooks/useApiCallContext";

type ContextTypeProps = {
  privileges: Privileges;
};

function ContextTypeContainer({ privileges }: ContextTypeProps) {
  const { apiCall } = useApiCall();
  const { getAllPermissions } = usePermissions();

  const { getList } = useContextType();

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(() => getList()).catch(noop);
    apiCall(() => getAllPermissions()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <>
      <CRUDPageContextProvider privileges={privileges}>
        <Table />
        <CRUD />
      </CRUDPageContextProvider>
    </>
  );
}

export default ContextTypeContainer;
