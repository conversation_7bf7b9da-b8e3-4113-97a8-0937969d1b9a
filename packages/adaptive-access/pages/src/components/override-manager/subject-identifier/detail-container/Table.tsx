import type React from "react";
import { useCallback, useContext, useMemo, useRef } from "react";
import {
  faEye,
  faPencilAlt,
  faRotateRight,
} from "@fortawesome/pro-solid-svg-icons";
import {
  Table<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Tooltip,
  Card,
} from "@zscaler/zui-component-library";
import { noop } from "lodash-es";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  useSubjectIdentifierContext,
  type TypePayload,
} from "../../../../ducks/subject-identifier";

import { CRUDPageContext } from "../../../../contexts/CRUDPageContextProvider";
import { useGlobalContext } from "../../../../ducks/global";
import { useSubjectIdentifierSelectors } from "../../../../ducks/subject-identifier/selectors";
import { useApiCall } from "../../../../hooks/useApiCallContext";

const Table: React.FC = () => {
  const { apiCall } = useApiCall();
  const { showErrorNotification, showSuccessNotification } = useGlobalContext();

  const { setModalMode, setDetail, detail, searchTerm, privileges } =
    useContext(CRUDPageContext)!;

  const { innerTableConfig: tableConfig, innerTableDetail: tableDetail } =
    useSubjectIdentifierSelectors();
  const { getList, deleteRow } = useSubjectIdentifierContext();

  const { hasFullAccess } = privileges;

  const onEditClick = useCallback(
    (details: TypePayload) => {
      if (details) {
        setDetail({ ...details, ...detail });
        setModalMode(hasFullAccess && !details.isSystemRole ? "edit" : "view");
      }
    },
    [detail, hasFullAccess, setDetail, setModalMode],
  );

  const onDeleteClick = useCallback((details: TypePayload) => {
    if (details) {
      apiCall(() => deleteRow(details))
        .then(() => {
          showSuccessNotification({
            message: "Removed overrides successfully",
          });
        })
        .catch((err: Error) => {
          showErrorNotification({
            message: err.message || "Error in deletion",
          });
        });
    }
  }, []);

  const renderTooltipSection = () => (
    <Card>
      <span>Remove the override values</span>
    </Card>
  );

  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail) => {
        if (columnDetail.id === "actions") {
          const ActionsCellComponent: React.FC<{
            row: { original: TypePayload };
          }> = (props) => {
            const isSystemRole = props?.row?.original?.isSystemRole;
            const isOverridable = props?.row?.original?.isOverridable;
            const buttonRef = useRef<HTMLDivElement | null>(null);

            return (
              <div className="flex items-center">
                <Button
                  disabled={!isOverridable}
                  type="tertiary"
                  onClick={() => onEditClick(props.row.original)}
                  containerClass="no-p-l content-width"
                  style={{
                    marginLeft: "8px",
                  }}
                >
                  <FontAwesomeIcon
                    icon={hasFullAccess && !isSystemRole ? faPencilAlt : faEye}
                  />
                </Button>

                {isOverridable && (
                  <div className="relative group" ref={buttonRef}>
                    <Button
                      type="tertiary"
                      onClick={() => onDeleteClick(props.row.original)}
                      containerClass="no-p-l content-width"
                      style={{
                        marginLeft: "8px",
                      }}
                    >
                      <FontAwesomeIcon icon={faRotateRight} />
                    </Button>
                    <Tooltip elementRef={buttonRef}>
                      {renderTooltipSection()}
                    </Tooltip>
                  </div>
                )}
              </div>
            );
          };

          columnDetail.cell = ActionsCellComponent;
        }

        return columnDetail;
      }) || [],
    [tableConfig?.columns, hasFullAccess, onEditClick, onDeleteClick],
  );

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload: {
      requireTotal: boolean;
      pageOffset?: number;
      pageSize?: number;
      name?: string;
    } = {
      requireTotal: false,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    if (pageOffset && pageSize) {
      payload.pageOffset = pageOffset + pageSize;
    }

    apiCall(() => getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default Table;
