import { useEffect } from "react";

import { noop } from "lodash-es";
import CRUDPageContextProvider from "../../../contexts/CRUDPageContextProvider";
import NoAccess from "../../no-access/NoAccess";
import { type Privileges } from "../../../types/permissions";
import Table from "./Table";
import CRUD from "./CRUD";
import { usePermissions } from "../../../ducks/permissions";
import { useSubjectIdentifierContext } from "../../../ducks/subject-identifier";
import { useApiCall } from "../../../hooks/useApiCallContext";

type SubjectIdentifierProps = {
  privileges: Privileges;
};

function SubjectIdentifierContainer({ privileges }: SubjectIdentifierProps) {
  const { apiCall } = useApiCall();
  const { getAllPermissions } = usePermissions();
  const { getList } = useSubjectIdentifierContext();

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(() => getList()).catch(noop);
    apiCall(() => getAllPermissions()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <>
      <CRUDPageContextProvider privileges={privileges}>
        <Table />
        <CRUD />
      </CRUDPageContextProvider>
    </>
  );
}

export default SubjectIdentifierContainer;
