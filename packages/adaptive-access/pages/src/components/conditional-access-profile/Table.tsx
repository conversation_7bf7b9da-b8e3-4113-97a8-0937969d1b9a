import type React from "react";
import { useCallback, useContext, useMemo } from "react";

import { faEye, faPencilAlt } from "@fortawesome/pro-solid-svg-icons";
import { faCopy } from "@fortawesome/pro-regular-svg-icons";

import {
  Actions,
  TableContainer,
  TextWithTooltip,
} from "@zscaler/zui-component-library";

import { noop } from "lodash-es";

import { CRUDPageContext } from "../../contexts/CRUDPageContextProvider";
import { type TableColumnDetail } from "../../types/table";
import { useProfileSelectors } from "../../ducks/conditional-access-profile/selectors";
import { useProfileContext } from "../../ducks/conditional-access-profile";
import { useGlobalContext } from "../../ducks/global";
import { useApiCall } from "../../hooks/useApiCallContext";

type RowData = {
  isSystemRole?: boolean;
  conditionJson?: string;
  name?: string;
  desc?: string;
  conditions?: string;
  [key: string]: unknown;
};

const Table: React.FC = () => {
  const { apiCall } = useApiCall();

  const { showErrorNotification, showSuccessNotification } = useGlobalContext();

  const { setModalMode, setDetail, searchTerm, privileges } =
    useContext(CRUDPageContext)!;

  const { tableDetail, tableConfig } = useProfileSelectors();
  const { getList } = useProfileContext();

  const { hasFullAccess } = privileges;

  const onEditClick = useCallback(
    (detail: RowData) => {
      if (detail) {
        setDetail(detail);
        setModalMode(hasFullAccess && !detail.isSystemRole ? "edit" : "view");
      }
    },
    [hasFullAccess, setDetail, setModalMode],
  );

  const onDeleteClick = useCallback(
    (detail: RowData) => {
      if (detail) {
        setDetail(detail);
        setModalMode("delete");
      }
    },
    [setDetail, setModalMode],
  );

  const tableColumnConfig = useMemo(
    () =>
      tableConfig?.columns?.map((columnDetail: TableColumnDetail) => {
        if (columnDetail.id === "actions") {
          const ActionsCellComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const isSystemRole = props?.row?.original?.isSystemRole;

            const handleCopy = () => {
              const jsonString = props?.row?.original?.conditionJson;
              navigator.clipboard
                .writeText(jsonString ?? "")
                .then(() => {
                  showSuccessNotification({ message: "Cloned Profile" });
                })
                .catch(() => {
                  showErrorNotification({ message: "Failed to clone" });
                });
            };

            return (
              <>
                <Actions
                  {...props}
                  showEdit={true}
                  editIcon={
                    hasFullAccess && !isSystemRole ? faPencilAlt : faEye
                  }
                  onEditClick={onEditClick}
                  onDeleteClick={onDeleteClick}
                  showDelete={hasFullAccess && !isSystemRole}
                  showAddUsers={true}
                  addUsersIcon={faCopy}
                  onAddUsersClick={handleCopy}
                />
              </>
            );
          };

          columnDetail.cell = ActionsCellComponent;
        }

        if (columnDetail.id === "name") {
          const TextWithTooltipComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const { name } = props?.row?.original || {};

            return <TextWithTooltip text={name ?? ""} />;
          };

          columnDetail.cell = TextWithTooltipComponent;
        }

        if (columnDetail.id === "desc") {
          const TextWithTooltipComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const { desc } = props?.row?.original || {};

            return <TextWithTooltip text={desc ?? ""} />;
          };

          columnDetail.cell = TextWithTooltipComponent;
        }

        if (columnDetail.id === "conditions") {
          const TextWithTooltipComponent: React.FC<{
            row: { original: RowData };
          }> = (props) => {
            const { conditions } = props?.row?.original || {};

            return <TextWithTooltip text={conditions ?? ""} />;
          };

          columnDetail.cell = TextWithTooltipComponent;
        }

        return columnDetail;
      }) || [],
    [tableConfig?.columns, hasFullAccess, onEditClick, onDeleteClick],
  );

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload: {
      requireTotal: boolean;
      pageOffset?: number;
      pageSize?: number;
      name?: string;
    } = {
      requireTotal: false,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    if (pageOffset && pageSize) {
      payload.pageOffset = pageOffset + pageSize;
    }

    apiCall(() => getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default Table;
