import { createAdaptiveAccessPageWrapper } from "./createPageWrapper";

// Optimized page wrappers with lazy loading and code splitting
export const IntegrationsPageWrapper = createAdaptiveAccessPageWrapper(
  () => import("../pages/IntegrationsPage").then(m => ({ default: m.default })),
  [
    () => import("../ducks/integrations").then(m => ({ IntegrationsProvider: m.IntegrationsProvider }))
  ],
  "integration-page"
);

export const ProfilesPageWrapper = createAdaptiveAccessPageWrapper(
  () => import("../pages/ProfilesPage").then(m => ({ default: m.default })),
  [
    () => import("../ducks/conditional-access-profile").then(m => ({ ProfileProvider: m.ProfileProvider }))
  ],
  "profiles-page"
);

export const OverrideManagerPageWrapper = createAdaptiveAccessPageWrapper(
  () => import("../pages/OverrideManagerPage").then(m => ({ default: m.default })),
  [
    () => import("../ducks/subject-identifier").then(m => ({ SubjectIdentifierProvider: m.SubjectIdentifierProvider })),
    () => import("../ducks/context-type").then(m => ({ ContextTypeProvider: m.ContextTypeProvider }))
  ],
  "overrides-page"
);

export const SignalHistoryPageWrapper = createAdaptiveAccessPageWrapper(
  () => import("../pages/SignalHistoryPage").then(m => ({ default: m.default })),
  [
    () => import("../ducks/signal-history").then(m => ({ SignalHistoryProvider: m.SignalHistoryProvider }))
  ],
  "signal-history-page"
);
