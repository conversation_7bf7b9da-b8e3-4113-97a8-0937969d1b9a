import React, { Suspense, lazy } from "react";
import type { ComponentType, ReactNode, FC } from "react";

// Types for the wrapper factory
interface PageWrapperConfig {
  pageComponent: () => Promise<{ default: ComponentType }>;
  providers?: Array<() => Promise<{ [key: string]: FC<{ children: ReactNode }> }>>;
  errorBoundary?: () => Promise<{ default: ComponentType }>;
  container?: () => Promise<{ ResponsiveContainer: ComponentType<{ id: string; children: ReactNode }> }>;
  containerId?: string;
  fallback?: ReactNode;
}

// Simple fallback component
const DefaultFallback = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    fontSize: '14px',
    color: '#666'
  }}>
    Loading...
  </div>
);

// Error fallback component
const DefaultErrorFallback = () => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    padding: '20px'
  }}>
    <h1>Oops! Something went wrong.</h1>
    <p>We are sorry for the inconvenience. Please try again later.</p>
  </div>
);

/**
 * Production-grade page wrapper factory
 * Creates optimized, lazy-loaded page components with proper error boundaries
 */
export function createPageWrapper(config: PageWrapperConfig): ComponentType {
  const {
    pageComponent,
    providers = [],
    errorBoundary,
    container,
    containerId = "page-container",
    fallback = <DefaultFallback />
  } = config;

  // Lazy load all components
  const LazyPage = lazy(pageComponent);
  const LazyErrorBoundary = errorBoundary ? lazy(errorBoundary) : null;
  const LazyContainer = container ? lazy(container) : null;
  const LazyProviders = providers.map(provider => lazy(provider));

  return function PageWrapper() {
    // Build the component tree from inside out
    let content: ReactNode = (
      <Suspense fallback={fallback}>
        <LazyPage />
      </Suspense>
    );

    // Wrap with container if provided
    if (LazyContainer) {
      content = (
        <Suspense fallback={fallback}>
          <LazyContainer id={containerId}>
            {content}
          </LazyContainer>
        </Suspense>
      );
    }

    // Wrap with providers (in reverse order so they nest correctly)
    for (let i = LazyProviders.length - 1; i >= 0; i--) {
      const LazyProvider = LazyProviders[i];
      content = (
        <Suspense fallback={fallback}>
          <LazyProvider>
            {content}
          </LazyProvider>
        </Suspense>
      );
    }

    // Wrap with error boundary if provided
    if (LazyErrorBoundary) {
      content = (
        <Suspense fallback={fallback}>
          <LazyErrorBoundary fallback={() => <DefaultErrorFallback />}>
            {content}
          </LazyErrorBoundary>
        </Suspense>
      );
    }

    return <>{content}</>;
  };
}

// Pre-configured wrapper for common patterns
export function createAdaptiveAccessPageWrapper(
  pageComponent: () => Promise<{ default: ComponentType }>,
  providers: Array<() => Promise<{ [key: string]: FC<{ children: ReactNode }> }>>,
  containerId: string
): ComponentType {
  return createPageWrapper({
    pageComponent,
    providers,
    errorBoundary: () => import("../components/error-boundary/ErrorBoundaryWrapper").then(m => ({ default: m.default })),
    container: () => import("../components/common/ResponsiveContainer").then(m => ({ ResponsiveContainer: m.ResponsiveContainer })),
    containerId,
    fallback: <DefaultFallback />
  });
}
