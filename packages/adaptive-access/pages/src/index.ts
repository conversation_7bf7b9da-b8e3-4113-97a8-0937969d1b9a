import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

import "./styles.css";

// Core exports - optimized for tree shaking
export { default as AppProvider } from "./zuxp-layout/AppProvider";
export { default as ErrorBoundaryWrapper } from "./components/error-boundary/ErrorBoundaryWrapper";
export { ResponsiveContainer } from "./components/common/ResponsiveContainer";
export { initializeHttpClient } from "./utils/http/http";
export { default as http } from "./utils/http/http";
export { type FeatureFlagsType } from "./zuxp-layout/FeatureFlagsProvider";

// Page components - these will be code-split by Next.js dynamic imports
export { default as SignalHistoryPage } from "./pages/SignalHistoryPage";
export { default as ProfilesPage } from "./pages/ProfilesPage";
export { default as IntegrationsPage } from "./pages/IntegrationsPage";
export { default as OverrideManagerPage } from "./pages/OverrideManagerPage";

// Providers - these will be code-split by Next.js dynamic imports
export { SignalHistoryProvider } from "./ducks/signal-history";
export { ProfileProvider } from "./ducks/conditional-access-profile";
export { IntegrationsProvider } from "./ducks/integrations";
export { SubjectIdentifierProvider } from "./ducks/subject-identifier";
export { ContextTypeProvider } from "./ducks/context-type";

// Production-grade optimized page wrappers
export {
  IntegrationsPageWrapper,
  ProfilesPageWrapper,
  OverrideManagerPageWrapper,
  SignalHistoryPageWrapper
} from "./wrappers";

// Utility function for class merging
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}
