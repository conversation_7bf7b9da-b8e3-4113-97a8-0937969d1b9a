"use client";
// eslint-disable-next-line import/no-unresolved
import { initializeHttpClient } from "@aae/aae-ui"; // Keep imports that don't require dynamic loading
import { getBearerToken } from "@up/std";
import environment from "@/utils/environment";

// eslint-disable-next-line import/no-unresolved
import "@aae/aae-ui/dist/index.css";

// Initialize the HTTP client outside the component
initializeHttpClient(environment, getBearerToken);

// eslint-disable-next-line import/no-unresolved
export { AppProvider as default } from "@aae/aae-ui";
