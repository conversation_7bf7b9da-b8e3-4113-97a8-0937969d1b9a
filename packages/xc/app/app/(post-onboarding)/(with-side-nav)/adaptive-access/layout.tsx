import dynamic from "next/dynamic";
import { type ReactNode } from "react";
import { type FeatureFlagsType } from "@aae/aae-ui";
import { Spinner } from "@/components/Spinner/Spinner";
import { useFlags } from "@/context/FeatureFlags";

type PageLayoutProps = {
  children: ReactNode;
};

const LayoutProvider = dynamic(() => import("./layoutProvider"), {
  ssr: false,
  loading: () => <Spinner size="2xl" defaultClass="min-h-[100vh]" />,
});

export default function Layout({ children }: PageLayoutProps) {
  const { can } = useFlags();
  const featureFlags: FeatureFlagsType = {
    isAAEIntegrationEnabled: can("isAAEIntegrationEnabled"),
  };

  return (
    <LayoutProvider featureFlags={featureFlags}>{children}</LayoutProvider>
  );
}
